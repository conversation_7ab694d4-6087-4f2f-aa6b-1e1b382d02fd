#!/usr/bin/env python
# -*- coding: utf-8 -*-

def interpolate_24_to_97_correct(data_24_hours):
    """正确的24点到97点插值"""
    result_97_points = []
    
    # 处理0-22点（92个点）
    for hour in range(23):
        current_value = data_24_hours[hour]
        next_value = data_24_hours[hour + 1]
        
        # 生成4个15分钟间隔点
        for minute_idx in range(4):
            ratio = minute_idx / 4.0  # 0, 0.25, 0.5, 0.75
            interpolated_value = current_value + (next_value - current_value) * ratio
            result_97_points.append(round(interpolated_value, 3))
    
    # 处理23点（4个点）+ 24点（1个点）
    hour_23_value = data_24_hours[23]
    hour_24_value = data_24_hours[23]  # 24点使用23点的值
    
    # 23点的4个15分钟点
    for minute_idx in range(4):
        ratio = minute_idx / 4.0  # 0, 0.25, 0.5, 0.75
        interpolated_value = hour_23_value + (hour_24_value - hour_23_value) * ratio
        result_97_points.append(round(interpolated_value, 3))
    
    # 添加24点
    result_97_points.append(round(hour_24_value, 3))
    
    return result_97_points

# 第三组数据
data_group_3 = [24.768, 24.04, 23.743, 23.275, 22.899, 23.071, 23.785, 24.689, 25.976, 27.263, 28.633, 29.836, 30.457, 30.704, 31.311, 31.407, 31.099, 29.315, 27.55, 26.621, 25.682, 25.596, 25.054, 24.343]

print("重新计算第三组数据的97点插值")
print("=" * 50)

# 插值
data_97 = interpolate_24_to_97_correct(data_group_3)

print(f"原始24点数据长度: {len(data_group_3)}")
print(f"插值后97点数据长度: {len(data_97)}")

if len(data_97) == 97:
    print("✅ 正确：97个点")
else:
    print(f"❌ 错误：应该是97个点，实际是{len(data_97)}个点")

print(f"\n第三组数据完整97点插值结果:")
print("[", end="")
for i, value in enumerate(data_97):
    if i > 0:
        print(", ", end="")
    if i % 10 == 0 and i > 0:
        print("\n ", end="")
    print(f"{value:.3f}", end="")
print("]")

print(f"\n\n验证关键点:")
print(f"第92个点 (23:00): {data_97[92]:.3f}")
print(f"第93个点 (23:15): {data_97[93]:.3f}")
print(f"第94个点 (23:30): {data_97[94]:.3f}")
print(f"第95个点 (23:45): {data_97[95]:.3f}")
print(f"第96个点 (24:00): {data_97[96]:.3f}")
print(f"原始23点值: {data_group_3[23]:.3f}")
