#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

print("=" * 60)
print("Canal导入检查脚本")
print("=" * 60)

# 检查Python环境
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print(f"当前工作目录: {os.getcwd()}")
print()

# 检查sys.path
print("Python模块搜索路径:")
for i, path in enumerate(sys.path):
    print(f"  {i+1}. {path}")
print()

# 尝试导入canal相关包
canal_packages = [
    'canal',
    'canal.client',
    'canal.protocol',
    'canal_python',
    'python_canal_client'
]

print("尝试导入canal相关包:")
for package in canal_packages:
    try:
        __import__(package)
        print(f"  ✅ {package} - 导入成功")
    except ImportError as e:
        print(f"  ❌ {package} - 导入失败: {e}")
    except Exception as e:
        print(f"  ⚠️  {package} - 其他错误: {e}")

print()

# 检查已安装的包
print("检查已安装的包中是否包含canal:")
try:
    import pkg_resources
    installed_packages = [pkg.project_name.lower() for pkg in pkg_resources.working_set]
    canal_related = [pkg for pkg in installed_packages if 'canal' in pkg]
    
    if canal_related:
        print("  找到canal相关包:")
        for pkg in canal_related:
            print(f"    - {pkg}")
    else:
        print("  ❌ 未找到canal相关包")
        
except Exception as e:
    print(f"  检查包列表时出错: {e}")

print()

# 尝试使用pip list检查
print("使用pip检查canal包:")
try:
    import subprocess
    result = subprocess.run(['pip', 'list'], capture_output=True, text=True)
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        canal_lines = [line for line in lines if 'canal' in line.lower()]
        if canal_lines:
            print("  找到canal相关包:")
            for line in canal_lines:
                print(f"    {line}")
        else:
            print("  ❌ pip list中未找到canal相关包")
    else:
        print(f"  pip命令执行失败: {result.stderr}")
except Exception as e:
    print(f"  执行pip命令时出错: {e}")

print()
print("=" * 60)
print("检查完成")
print("=" * 60)
