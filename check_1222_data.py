#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MySQL中2024-12-22的原始数据
"""

import pymysql
import json


from canal.client import Client
from canal.protocol import EntryProtocol_pb2
from canal.protocol import CanalProtocol_pb2


def connect_to_mysql():
    """连接到MySQL数据库"""
    try:
        connection = pymysql.connect(
            host="*************",
            port=9030,
            user="heilongjiang",
            password="hlj123@#$",
            database="tsintergy_datawarehouse",
            charset='utf8mb4'
        )
        print("✅ MySQL数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def check_1222_data(conn):
    """检查2024-12-22的数据"""
    cursor = conn.cursor()
    
    print("🔍 检查2024-12-22的湿度数据:")
    
    # 查找2024-12-22的所有湿度数据
    cursor.execute("""
        SELECT 
            location_id,
            DATE(date) as data_date,
            data
        FROM dwd_weather_history_info 
        WHERE DATE(date) = '2024-12-22'
        AND source_type = 'CMA'
        AND weather_type = 'RH'
        ORDER BY location_id
    """)
    
    results = cursor.fetchall()
    if results:
        print(f"找到 {len(results)} 个地区在2024-12-22有湿度数据:")
        
        for result in results:
            location_id = result[0]
            data_date = result[1]
            data = result[2]
            
            print(f"\n地区ID {location_id} - {data_date}:")
            
            try:
                # 解析数据
                if isinstance(data, str):
                    parsed_data = json.loads(data)
                else:
                    parsed_data = data
                
                if isinstance(parsed_data, list):
                    print(f"  数据长度: {len(parsed_data)}")
                    
                    # 检查null值
                    null_count = parsed_data.count(None)
                    null_positions = [i for i, x in enumerate(parsed_data) if x is None]
                    
                    if null_count > 0:
                        print(f"  ❌ 包含 {null_count} 个null值")
                        print(f"  null值位置: {null_positions}")
                        print(f"  原始数据: {parsed_data}")
                    else:
                        print(f"  ✅ 无null值")
                        valid_data = [x for x in parsed_data if x is not None]
                        if valid_data:
                            print(f"  数据范围: {min(valid_data):.1f}% - {max(valid_data):.1f}%")
                        print(f"  前5个值: {parsed_data[:5]}")
                        print(f"  后5个值: {parsed_data[-5:]}")
                else:
                    print(f"  ❌ 数据格式错误: {type(parsed_data)}")
                    print(f"  原始数据: {data}")
                    
            except Exception as e:
                print(f"  ❌ 数据解析失败: {e}")
                print(f"  原始数据类型: {type(data)}")
                print(f"  原始数据: {data}")
    else:
        print("❌ 2024-12-22没有找到任何湿度数据")
    
    cursor.close()

def main():
    conn = connect_to_mysql()
    if conn:
        check_1222_data(conn)
        conn.close()

if __name__ == "__main__":
    main()
