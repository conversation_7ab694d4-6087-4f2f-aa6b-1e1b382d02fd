#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import site

def find_package_in_site_packages(package_name):
    """查找包在site-packages中的结构"""
    print(f"查找包: {package_name}")
    print("=" * 50)
    
    # 获取所有site-packages路径
    site_packages_paths = site.getsitepackages()
    if hasattr(site, 'getusersitepackages'):
        site_packages_paths.append(site.getusersitepackages())
    
    print("site-packages路径:")
    for path in site_packages_paths:
        print(f"  {path}")
    print()
    
    found_packages = []
    
    for site_path in site_packages_paths:
        if os.path.exists(site_path):
            print(f"检查路径: {site_path}")
            
            # 查找包含指定名称的目录和文件
            for item in os.listdir(site_path):
                if package_name.lower() in item.lower():
                    item_path = os.path.join(site_path, item)
                    found_packages.append(item_path)
                    print(f"  找到: {item}")
                    
                    # 如果是目录，显示内部结构
                    if os.path.isdir(item_path):
                        print(f"    目录结构:")
                        try:
                            for root, dirs, files in os.walk(item_path):
                                level = root.replace(item_path, '').count(os.sep)
                                indent = '      ' + '  ' * level
                                print(f"{indent}{os.path.basename(root)}/")
                                
                                # 只显示Python文件
                                subindent = '      ' + '  ' * (level + 1)
                                for file in files:
                                    if file.endswith('.py'):
                                        print(f"{subindent}{file}")
                                
                                # 限制深度，避免输出过多
                                if level > 2:
                                    break
                        except PermissionError:
                            print(f"      权限不足，无法访问")
                        except Exception as e:
                            print(f"      错误: {e}")
            print()
    
    return found_packages

def show_import_examples(package_name):
    """显示常见的导入示例"""
    print(f"\n{package_name} 常见导入方式:")
    print("=" * 50)
    
    import_examples = [
        f"import {package_name}",
        f"from {package_name} import *",
        f"from {package_name} import Client",
        f"from {package_name} import client",
        f"from {package_name}.client import Client",
        f"from {package_name}.protocol import *",
    ]
    
    for example in import_examples:
        try:
            exec(example)
            print(f"✅ {example}")
        except ImportError as e:
            print(f"❌ {example} - {e}")
        except Exception as e:
            print(f"⚠️  {example} - {e}")

if __name__ == "__main__":
    # 查找canal相关包
    package_names = ['canal', 'python-canal', 'pycanalclient']
    
    for pkg_name in package_names:
        found = find_package_in_site_packages(pkg_name)
        if found:
            show_import_examples(pkg_name.replace('-', '_'))
        print("\n" + "="*60 + "\n")
