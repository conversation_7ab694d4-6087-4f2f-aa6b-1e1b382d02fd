#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将24点数据插值为97点数据
"""

def interpolate_24_to_97(data_24_hours):
    """
    将24小时数据插值为97个时间点
    :param data_24_hours: 24小时数据列表（00-23点）
    :return: 97个时间点的数据列表
    """
    if len(data_24_hours) != 24:
        raise ValueError("输入数据必须是24个小时的数据")
    
    result_97_points = []
    
    # 处理0-22点（92个点）
    for hour in range(23):
        current_value = data_24_hours[hour]
        next_value = data_24_hours[hour + 1]
        
        # 生成4个15分钟间隔点
        for minute_idx in range(4):
            ratio = minute_idx / 4.0  # 0, 0.25, 0.5, 0.75
            interpolated_value = current_value + (next_value - current_value) * ratio
            result_97_points.append(round(interpolated_value, 3))
    
    # 处理23点（4个点）+ 24点（1个点）
    # 23点的值
    hour_23_value = data_24_hours[23]
    # 24点使用23点的值
    hour_24_value = data_24_hours[23]
    
    # 23点的4个15分钟点
    for minute_idx in range(4):
        ratio = minute_idx / 4.0  # 0, 0.25, 0.5, 0.75
        interpolated_value = hour_23_value + (hour_24_value - hour_23_value) * ratio
        result_97_points.append(round(interpolated_value, 3))
    
    # 添加24点
    result_97_points.append(round(hour_24_value, 3))
    
    return result_97_points

def generate_time_labels():
    """生成97个时间点标签"""
    time_labels = []
    
    # 0-22点，每小时4个点
    for hour in range(23):
        for minute in [0, 15, 30, 45]:
            time_labels.append(f"{hour:02d}:{minute:02d}")
    
    # 23点的4个点
    for minute in [0, 15, 30, 45]:
        time_labels.append(f"23:{minute:02d}")
    
    # 24点
    time_labels.append("24:00")
    
    return time_labels

def main():
    # 您提供的3组24小时数据
    data_group_1 = [23.06, 22.76, 22.363, 22.244, 22.074, 22.246, 22.995, 23.686, 24.575, 25.397, 25.991, 26.648, 27.211, 27.541, 27.941, 28.16, 28.172, 27.521, 26.705, 25.459, 24.189, 23.478, 23.064, 22.792]
    
    data_group_2 = [22.366, 22.329, 22.145, 22.009, 21.902, 21.914, 22.425, 23.451, 24.413, 25.417, 25.832, 26.124, 25.798, 25.493, 24.911, 24.439, 24.143, 24.009, 23.339, 22.496, 21.951, 21.618, 21.388, 21.127]
    
    data_group_3 = [24.768, 24.04, 23.743, 23.275, 22.899, 23.071, 23.785, 24.689, 25.976, 27.263, 28.633, 29.836, 30.457, 30.704, 31.311, 31.407, 31.099, 29.315, 27.55, 26.621, 25.682, 25.596, 25.054, 24.343]
    
    # 生成时间标签
    time_labels = generate_time_labels()
    
    print("24点数据插值为97点数据")
    print("=" * 60)
    
    # 处理每组数据
    data_groups = [data_group_1, data_group_2, data_group_3]
    
    for group_idx, data_24 in enumerate(data_groups, 1):
        print(f"\n数据组 {group_idx}:")
        print(f"原始24点数据: {len(data_24)} 个点")
        
        # 插值为97点
        data_97 = interpolate_24_to_97(data_24)
        
        print(f"插值后97点数据: {len(data_97)} 个点")
        print(f"数据范围: {min(data_97):.3f} - {max(data_97):.3f}")
        
        # 显示前10个和后10个时间点的数据
        print("\n前10个时间点:")
        for i in range(10):
            print(f"  {time_labels[i]}: {data_97[i]:.3f}")
        
        print("\n后10个时间点:")
        for i in range(87, 97):
            print(f"  {time_labels[i]}: {data_97[i]:.3f}")
        
        # 验证23点和24点的处理
        print(f"\n23点数据验证:")
        print(f"  原始23点值: {data_24[23]:.3f}")
        print(f"  23:00点: {data_97[92]:.3f}")
        print(f"  23:15点: {data_97[93]:.3f}")
        print(f"  23:30点: {data_97[94]:.3f}")
        print(f"  23:45点: {data_97[95]:.3f}")
        print(f"  24:00点: {data_97[96]:.3f}")
        
        # 输出完整的97点数据（用于复制）
        print(f"\n数据组 {group_idx} 完整97点数据 (共{len(data_97)}个点):")
        print("[", end="")
        for i, value in enumerate(data_97):
            if i > 0:
                print(", ", end="")
            if i % 10 == 0 and i > 0:
                print("\n ", end="")
            print(f"{value:.3f}", end="")
        print("]")

        # 验证点数
        if len(data_97) != 97:
            print(f"❌ 错误：应该是97个点，实际是{len(data_97)}个点")
        
        print("\n" + "-" * 60)

if __name__ == "__main__":
    main()
