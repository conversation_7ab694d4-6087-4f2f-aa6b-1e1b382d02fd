#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示Excel输出和指标选择功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_demo_data():
    """创建演示数据"""
    print("🎬 创建演示数据...")
    
    columns = [
        "station_id", "station_name", "station_type", "province", "city", "district", 
        "lon", "lat", "altitude", "pressure_altitude", "adcode", "note", "update_time", 
        "station_id", "time", "prs", "prs_sea", "win_s_max", "win_d_s_max", 
        "win_s_lnst_max", "win_d_lnst_max", "win_s_avg_2mi", "win_d_avg_2mi", 
        "tem", "rhu", "pre_1h", "vis", "update_time"
    ]
    
    data = []
    
    # 生成长春市1天数据，3个站点
    for hour in range(24):
        for station in range(3):
            time_str = f"2025/1/1 {hour:02d}:00:00"
            
            # 模拟真实的气象变化
            base_temp = -15.0 + hour * 0.8 + np.random.normal(0, 0.5)  # 温度随时间变化
            base_humidity = 75 + np.random.normal(0, 3)  # 湿度
            base_pressure = 1015 + np.random.normal(0, 1)  # 气压
            
            row = [
                f"5418{station+1}", f"长春站{station+1}", "A", "吉林省", "长春市", "长春市",
                "125.3", "43.8", "200", "200", "220100", "", "2024/11/19 18:13:46",
                f"5418{station+1}", time_str,
                round(base_pressure, 1),                     # prs
                round(base_pressure + 13, 1),                # prs_sea
                round(2.0 + np.random.normal(0, 0.5), 1),    # win_s_max
                round(180 + np.random.normal(0, 30), 0),     # win_d_s_max
                round(3.0 + np.random.normal(0, 0.8), 1),    # win_s_lnst_max
                round(200 + np.random.normal(0, 40), 0),     # win_d_lnst_max
                round(1.5 + np.random.normal(0, 0.3), 1),    # win_s_avg_2mi
                round(190 + np.random.normal(0, 20), 0),     # win_d_avg_2mi
                round(base_temp, 1),                         # tem
                round(max(30, min(95, base_humidity)), 0),   # rhu
                round(max(0, np.random.exponential(0.05)), 1), # pre_1h
                "10000",                                     # vis
                "2025/4/16 19:50:20"
            ]
            data.append(row)
    
    df = pd.DataFrame(data, columns=columns)
    df.to_csv("吉林cma实测气象.csv", index=False, encoding='utf-8-sig')
    
    print(f"✅ 演示数据已创建: {df.shape}")
    return df

def demo_all_indicators():
    """演示所有指标解析"""
    print("\n🎬 演示1: 解析所有气象指标")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        print("📊 正在解析所有气象指标...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator=None
        )
        
        if results:
            print("✅ 解析成功！")
            excel_file = "长春市_20250101_20250101_97points.xlsx"
            
            if os.path.exists(excel_file):
                print(f"📄 Excel文件: {excel_file}")
                
                # 显示Excel内容
                xl_file = pd.ExcelFile(excel_file)
                print(f"📋 包含工作表: {xl_file.sheet_names}")
                
                # 读取主数据
                main_data = pd.read_excel(excel_file, sheet_name='气象数据')
                print(f"📊 主数据: {main_data.shape[0]} 行 × {main_data.shape[1]} 列")
                
                # 显示气象指标列
                weather_cols = [col for col in main_data.columns if col not in ['城市', '日期', '时间']]
                print(f"🌤️  气象指标: {weather_cols}")
                
                return excel_file
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def demo_temperature_only():
    """演示只解析温度指标"""
    print("\n🎬 演示2: 只解析温度指标")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        print("🌡️  正在解析温度指标...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator="tem"
        )
        
        if results:
            print("✅ 温度解析成功！")
            excel_file = "长春市_20250101_20250101_tem_97points.xlsx"
            
            if os.path.exists(excel_file):
                print(f"📄 Excel文件: {excel_file}")
                
                xl_file = pd.ExcelFile(excel_file)
                print(f"📋 包含工作表: {xl_file.sheet_names}")
                
                # 读取温度专门数据
                if 'tem数据' in xl_file.sheet_names:
                    tem_data = pd.read_excel(excel_file, sheet_name='tem数据')
                    print(f"🌡️  温度数据: {tem_data.shape[0]} 行")
                    
                    # 显示温度变化
                    print("\n🌡️  温度变化趋势（前20个点）:")
                    for i in range(min(20, len(tem_data))):
                        time_point = tem_data.iloc[i]['时间']
                        temp = tem_data.iloc[i]['tem']
                        print(f"    {time_point}: {temp}°C")
                
                return excel_file
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def demo_humidity_only():
    """演示只解析湿度指标"""
    print("\n🎬 演示3: 只解析湿度指标")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        print("💧 正在解析湿度指标...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator="rhu"
        )
        
        if results:
            print("✅ 湿度解析成功！")
            excel_file = "长春市_20250101_20250101_rhu_97points.xlsx"
            
            if os.path.exists(excel_file):
                print(f"📄 Excel文件: {excel_file}")
                return excel_file
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例:")
    print("=" * 30)
    
    print("1️⃣  交互式使用:")
    print("   python interactive_weather_parser.py")
    print("   然后按提示输入城市、日期和指标")
    
    print("\n2️⃣  命令行使用:")
    print("   # 所有指标")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250101")
    
    print("\n   # 只处理温度")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250101 --indicator tem")
    
    print("\n   # 只处理湿度")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250101 --indicator rhu")
    
    print("\n3️⃣  可用的气象指标:")
    indicators = [
        "tem (温度)", "rhu (湿度)", "prs (气压)", "prs_sea (海平面气压)",
        "win_s_avg_2mi (平均风速)", "win_d_avg_2mi (平均风向)",
        "pre_1h (降水量)", "vis (能见度)"
    ]
    
    for indicator in indicators:
        print(f"   • {indicator}")

def main():
    """主演示函数"""
    print("🎬 吉林CMA气象数据解析器 - Excel功能演示")
    print("=" * 60)
    
    # 创建演示数据
    create_demo_data()
    
    # 演示1: 所有指标
    demo_all_indicators()
    
    # 演示2: 温度指标
    demo_temperature_only()
    
    # 演示3: 湿度指标
    demo_humidity_only()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n📋 生成的文件:")
    
    # 列出生成的Excel文件
    excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and '长春市' in f]
    for file in excel_files:
        print(f"  📄 {file}")
    
    print(f"\n💡 提示: 可以用Excel打开这些文件查看详细数据")

if __name__ == "__main__":
    main()
