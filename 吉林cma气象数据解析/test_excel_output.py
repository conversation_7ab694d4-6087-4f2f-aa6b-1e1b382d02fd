#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Excel输出和指标选择功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 真实列名
    columns = [
        "station_id", "station_name", "station_type", "province", "city", "district", 
        "lon", "lat", "altitude", "pressure_altitude", "adcode", "note", "update_time", 
        "station_id", "time", "prs", "prs_sea", "win_s_max", "win_d_s_max", 
        "win_s_lnst_max", "win_d_lnst_max", "win_s_avg_2mi", "win_d_avg_2mi", 
        "tem", "rhu", "pre_1h", "vis", "update_time"
    ]
    
    data = []
    
    # 生成长春市2天数据，3个站点
    for day in range(2):
        for hour in range(24):
            for station in range(3):
                time_str = f"2025/1/{day+1} {hour:02d}:00:00"
                
                # 生成模拟气象数据
                base_temp = -15.0 + hour * 0.8 + np.random.normal(0, 1)
                
                row = [
                    f"5418{station+1}", f"长春站{station+1}", "A", "吉林省", "长春市", "长春市",
                    "125.3", "43.8", "200", "200", "220100", "", "2024/11/19 18:13:46",
                    f"5418{station+1}", time_str,
                    round(960.0 + np.random.normal(0, 2), 1),    # prs 气压
                    round(1028.0 + np.random.normal(0, 2), 1),   # prs_sea 海平面气压
                    round(1.5 + np.random.normal(0, 0.5), 1),    # win_s_max 最大风速
                    round(212 + np.random.normal(0, 20), 0),     # win_d_s_max 最大风速风向
                    round(2.9 + np.random.normal(0, 0.5), 1),    # win_s_lnst_max 瞬时最大风速
                    round(260 + np.random.normal(0, 20), 0),     # win_d_lnst_max 瞬时最大风速风向
                    round(1.6 + np.random.normal(0, 0.3), 1),    # win_s_avg_2mi 2分钟平均风速
                    round(264 + np.random.normal(0, 15), 0),     # win_d_avg_2mi 2分钟平均风向
                    round(base_temp, 1),                         # tem 温度
                    round(80 + np.random.normal(0, 5), 0),       # rhu 相对湿度
                    round(np.random.exponential(0.1), 1),        # pre_1h 1小时降水量
                    "999999",                                    # vis 能见度
                    "2025/4/16 19:50:20"                        # update_time
                ]
                data.append(row)
    
    df = pd.DataFrame(data, columns=columns)
    df.to_csv("吉林cma实测气象.csv", index=False, encoding='utf-8-sig')
    
    print(f"✅ 测试数据已创建: {df.shape}")
    print(f"🏙️  城市: {df['city'].unique()}")
    print(f"📅 时间范围: {df['time'].min()} 到 {df['time'].max()}")
    
    return df

def test_all_indicators():
    """测试所有指标输出"""
    print("\n🧪 测试1: 所有指标输出到Excel")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250102",
            weather_indicator=None  # 所有指标
        )
        
        if results:
            print("✅ 所有指标解析成功")
            
            # 检查生成的Excel文件
            excel_file = "长春市_20250101_20250102_97points.xlsx"
            if os.path.exists(excel_file):
                print(f"📄 Excel文件已生成: {excel_file}")
                
                # 读取Excel文件检查内容
                try:
                    xl_file = pd.ExcelFile(excel_file)
                    print(f"📋 Excel工作表: {xl_file.sheet_names}")
                    
                    # 读取主数据表
                    main_data = pd.read_excel(excel_file, sheet_name='气象数据')
                    print(f"📊 主数据表形状: {main_data.shape}")
                    print(f"📋 主数据表列名: {list(main_data.columns)}")
                    
                    # 显示部分数据
                    print("\n📄 前5行数据:")
                    print(main_data.head())
                    
                except Exception as e:
                    print(f"❌ 读取Excel文件失败: {e}")
            else:
                print("❌ Excel文件未生成")
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_single_indicator():
    """测试单一指标输出"""
    print("\n🧪 测试2: 单一指标(温度)输出到Excel")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator="tem"  # 只处理温度
        )
        
        if results:
            print("✅ 温度指标解析成功")
            
            # 检查生成的Excel文件
            excel_file = "长春市_20250101_20250101_tem_97points.xlsx"
            if os.path.exists(excel_file):
                print(f"📄 Excel文件已生成: {excel_file}")
                
                try:
                    xl_file = pd.ExcelFile(excel_file)
                    print(f"📋 Excel工作表: {xl_file.sheet_names}")
                    
                    # 读取温度专门数据表
                    if 'tem数据' in xl_file.sheet_names:
                        tem_data = pd.read_excel(excel_file, sheet_name='tem数据')
                        print(f"🌡️  温度数据表形状: {tem_data.shape}")
                        print(f"📋 温度数据表列名: {list(tem_data.columns)}")
                        
                        print("\n🌡️  温度数据前10行:")
                        print(tem_data.head(10))
                    
                except Exception as e:
                    print(f"❌ 读取Excel文件失败: {e}")
            else:
                print("❌ Excel文件未生成")
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_humidity_indicator():
    """测试湿度指标输出"""
    print("\n🧪 测试3: 湿度指标输出到Excel")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator="rhu"  # 只处理湿度
        )
        
        if results:
            print("✅ 湿度指标解析成功")
            
            # 检查生成的Excel文件
            excel_file = "长春市_20250101_20250101_rhu_97points.xlsx"
            if os.path.exists(excel_file):
                print(f"📄 Excel文件已生成: {excel_file}")
                print("💧 湿度数据解析完成")
            else:
                print("❌ Excel文件未生成")
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_available_indicators():
    """显示可用的气象指标"""
    print("\n📊 可用的气象指标:")
    print("=" * 30)
    
    indicators = {
        'tem': '温度 (°C)',
        'rhu': '相对湿度 (%)', 
        'prs': '气压 (hPa)',
        'prs_sea': '海平面气压 (hPa)',
        'win_s_max': '最大风速 (m/s)',
        'win_d_s_max': '最大风速风向 (°)',
        'win_s_lnst_max': '瞬时最大风速 (m/s)',
        'win_d_lnst_max': '瞬时最大风速风向 (°)',
        'win_s_avg_2mi': '2分钟平均风速 (m/s)',
        'win_d_avg_2mi': '2分钟平均风向 (°)',
        'pre_1h': '1小时降水量 (mm)',
        'vis': '能见度 (m)'
    }
    
    for code, name in indicators.items():
        print(f"  {code:15} : {name}")

def main():
    """主测试函数"""
    print("🧪 Excel输出和指标选择功能测试")
    print("=" * 50)
    
    # 创建测试数据
    create_test_data()
    
    # 显示可用指标
    show_available_indicators()
    
    # 测试1: 所有指标
    test_all_indicators()
    
    # 测试2: 温度指标
    test_single_indicator()
    
    # 测试3: 湿度指标
    test_humidity_indicator()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
    print("\n💡 使用说明:")
    print("1. 交互式使用: python interactive_weather_parser.py")
    print("2. 命令行使用: python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --indicator tem")
    print("3. 输出格式: Excel文件(.xlsx)，包含多个工作表")

if __name__ == "__main__":
    main()
