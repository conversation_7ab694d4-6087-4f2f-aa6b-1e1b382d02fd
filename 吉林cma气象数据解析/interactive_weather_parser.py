#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
吉林CMA实测气象数据交互式解析器
支持外部输入城市名称和日期范围，自动解析对应的地市气象数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
import argparse

def parse_weather_data(csv_file, city_name, start_date, end_date, weather_indicator=None, city_id=None, output_format='excel'):
    """
    解析气象数据的主函数
    :param csv_file: CSV文件路径
    :param city_name: 城市名称
    :param start_date: 开始日期 (YYYYMMDD)
    :param end_date: 结束日期 (YYYYMMDD)
    :param weather_indicator: 指定的气象指标，如'tem'(温度)、'rhu'(湿度)等，None表示所有指标
    :param city_id: 城市ID（SQL格式需要）
    :param output_format: 输出格式 ('excel' 或 'sql')
    """
    print(f"🌤️  开始解析 {city_name} 从 {start_date} 到 {end_date} 的气象数据")
    if weather_indicator:
        print(f"📊 指定气象指标: {weather_indicator}")
    else:
        print(f"📊 解析主要气象指标: 温度、湿度、降雨、风速")
    print(f"📄 输出格式: {output_format.upper()}")
    if city_id:
        print(f"🏙️  城市ID: {city_id}")
    print("=" * 60)
    
    try:
        # 1. 加载数据
        print("📂 正在加载数据...")
        data = load_csv_data(csv_file)
        
        # 2. 数据预处理
        print("🔧 正在预处理数据...")
        processed_data = preprocess_data(data)
        
        # 3. 筛选城市和日期数据
        print(f"🏙️  正在筛选 {city_name} 的数据...")
        city_data = filter_city_data(processed_data, city_name, start_date, end_date)
        
        if city_data.empty:
            print(f"❌ 未找到 {city_name} 在指定日期范围内的数据")
            return None
        
        # 4. 按站点平均计算
        print("📊 正在计算站点平均值...")
        averaged_data = calculate_station_average(city_data, weather_indicator)

        # 5. 转换为97点数据
        print("⏰ 正在转换为97点数据...")
        result_97_points = convert_to_97_points(averaged_data, weather_indicator)

        # 6. 导出结果
        output_file = export_results(result_97_points, city_name, start_date, end_date, weather_indicator)
        
        print("✅ 解析完成！")
        print(f"📄 结果文件: {output_file}")
        
        return result_97_points
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return None

def load_csv_data(csv_file):
    """加载CSV数据，自动检测编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    
    for encoding in encodings:
        try:
            data = pd.read_csv(csv_file, encoding=encoding)
            print(f"  ✅ 使用 {encoding} 编码成功加载")
            print(f"  📊 数据形状: {data.shape}")
            return data
        except UnicodeDecodeError:
            continue
    
    raise Exception("无法使用任何编码方式读取文件")

def preprocess_data(data):
    """数据预处理"""
    # 显示列名，帮助理解数据结构
    print(f"  📋 列名: {list(data.columns)}")
    
    # 这里需要根据实际CSV文件结构进行调整
    # 假设CSV包含以下列：站点名称、城市、日期、小时、温度等气象要素
    
    # 示例预处理步骤
    processed_data = data.copy()
    
    # 如果有日期时间列，转换格式
    date_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['date', '日期', '时间'])]
    for col in date_columns:
        try:
            processed_data[col] = pd.to_datetime(processed_data[col])
        except:
            pass
    
    return processed_data

def filter_city_data(data, city_name, start_date, end_date):
    """筛选指定城市和日期范围的数据"""
    # 转换日期格式
    start_dt = datetime.strptime(start_date, '%Y%m%d')
    end_dt = datetime.strptime(end_date, '%Y%m%d')

    filtered_data = data.copy()

    print(f"  📋 原始数据列名: {list(data.columns)}")

    # 根据真实列名筛选城市数据 - 使用 'city' 列
    if 'city' in data.columns:
        print(f"  🏙️  使用列 'city' 筛选城市")
        print(f"  🏙️  数据中的城市: {data['city'].unique()}")
        filtered_data = filtered_data[filtered_data['city'].str.contains(city_name, na=False)]
        print(f"  🏙️  筛选 {city_name} 后: {filtered_data.shape[0]} 条记录")
    else:
        print("  ⚠️  未找到 'city' 列，将使用所有数据")

    # 根据真实列名筛选日期 - 使用 'time' 列
    if 'time' in data.columns:
        print(f"  📅 使用列 'time' 筛选日期")
        print(f"  📅 时间列样本数据: {data['time'].head().tolist()}")

        try:
            # 转换时间格式 - 处理 "2023/1/1 00:00:00" 格式
            filtered_data['time'] = pd.to_datetime(filtered_data['time'])

            # 只比较日期部分
            filtered_data = filtered_data[
                (filtered_data['time'].dt.date >= start_dt.date()) &
                (filtered_data['time'].dt.date <= end_dt.date())
            ]
            print(f"  📅 日期筛选后: {filtered_data.shape[0]} 条记录")

        except Exception as e:
            print(f"  ⚠️  日期格式转换失败: {e}")
            print(f"  📅 尝试其他日期格式...")

            # 尝试其他可能的日期格式
            try:
                filtered_data['time'] = pd.to_datetime(filtered_data['time'], format='%Y/%m/%d %H:%M:%S')
                filtered_data = filtered_data[
                    (filtered_data['time'].dt.date >= start_dt.date()) &
                    (filtered_data['time'].dt.date <= end_dt.date())
                ]
                print(f"  ✅ 使用格式 '%Y/%m/%d %H:%M:%S' 成功")
            except Exception as e2:
                print(f"  ❌ 所有日期格式都失败: {e2}")
    else:
        print("  ⚠️  未找到 'time' 列")

    print(f"  📊 最终筛选后数据形状: {filtered_data.shape}")
    return filtered_data

def calculate_station_average(city_data, weather_indicator=None):
    """按站点平均计算地市气象数据"""
    if city_data.empty:
        print("  ❌ 没有数据可处理")
        return pd.DataFrame()

    # 根据真实数据结构识别气象要素列
    all_weather_columns = ['prs', 'prs_sea', 'win_s_max', 'win_d_s_max', 'win_s_lnst_max',
                          'win_d_lnst_max', 'win_s_avg_2mi', 'win_d_avg_2mi', 'tem', 'rhu',
                          'pre_1h', 'vis']

    # 如果指定了特定指标，只处理该指标
    if weather_indicator:
        if weather_indicator in all_weather_columns and weather_indicator in city_data.columns:
            available_weather_columns = [weather_indicator]
            print(f"  📊 处理指定气象指标: {weather_indicator}")
        else:
            print(f"  ❌ 指定的气象指标 '{weather_indicator}' 不存在")
            return pd.DataFrame()
    else:
        # 选择所有存在的气象列
        available_weather_columns = [col for col in all_weather_columns if col in city_data.columns]
        print(f"  📊 可用气象要素列: {available_weather_columns}")

    if not available_weather_columns:
        print("  ❌ 未找到气象要素列")
        return pd.DataFrame()

    # 确保time列存在并转换为datetime
    if 'time' not in city_data.columns:
        print("  ❌ 未找到time列")
        return pd.DataFrame()

    # 提取日期和小时
    city_data = city_data.copy()
    city_data['time'] = pd.to_datetime(city_data['time'])
    city_data['date'] = city_data['time'].dt.date
    city_data['hour'] = city_data['time'].dt.hour

    print(f"  📅 数据日期范围: {city_data['date'].min()} 到 {city_data['date'].max()}")
    print(f"  ⏰ 数据小时范围: {city_data['hour'].min()} 到 {city_data['hour'].max()}")

    # 按日期和小时分组，计算站点平均值
    print(f"  📊 按日期和小时分组计算平均值...")
    grouped_data = city_data.groupby(['date', 'hour'])[available_weather_columns].mean().reset_index()

    print(f"  ✅ 计算完成，共 {len(grouped_data)} 条平均数据")
    print(f"  📊 平均后数据形状: {grouped_data.shape}")

    # 显示部分结果
    if not grouped_data.empty:
        print(f"  📄 前5条平均数据:")
        print(grouped_data.head())

    return grouped_data

def convert_to_97_points(daily_data, weather_indicator=None):
    """将24点数据转换为97点数据"""
    print("  ⏰ 开始24点到97点转换...")

    if daily_data.empty:
        print("  ❌ 没有数据可转换")
        return []

    result_data = []

    # 按日期分组处理
    if 'date' in daily_data.columns:
        dates = sorted(daily_data['date'].unique())
        print(f"  📅 处理日期: {dates}")

        for i, date in enumerate(dates):
            day_data = daily_data[daily_data['date'] == date].copy()

            # 按小时排序
            day_data = day_data.sort_values('hour')

            print(f"  📅 处理日期 {date}: {len(day_data)} 条记录")
            print(f"  ⏰ 小时范围: {day_data['hour'].min()} - {day_data['hour'].max()}")

            # 检查是否有足够的小时数据
            if len(day_data) >= 20:  # 至少需要20小时数据
                # 获取下一天的00点数据（用于2400点）
                next_day_00_data = None
                if i + 1 < len(dates):
                    next_date = dates[i + 1]
                    next_day_data = daily_data[
                        (daily_data['date'] == next_date) &
                        (daily_data['hour'] == 0)
                    ]
                    if not next_day_data.empty:
                        next_day_00_data = next_day_data.iloc[0]

                day_97_points = generate_97_points_for_day(day_data, date, next_day_00_data, weather_indicator)
                result_data.append(day_97_points)
            else:
                print(f"  ⚠️  {date} 数据不足，跳过")

    print(f"  ✅ 转换完成，共 {len(result_data)} 天数据")
    return result_data

def generate_97_points_for_day(day_data, date, next_day_00_data=None, weather_indicator=None):
    """为单天生成97个时间点的数据"""
    # 气象要素列
    all_weather_columns = ['prs', 'prs_sea', 'win_s_max', 'win_d_s_max', 'win_s_lnst_max',
                          'win_d_lnst_max', 'win_s_avg_2mi', 'win_d_avg_2mi', 'tem', 'rhu',
                          'pre_1h', 'vis']

    # 根据指定指标选择列
    if weather_indicator:
        available_columns = [weather_indicator] if weather_indicator in day_data.columns else []
    else:
        available_columns = [col for col in all_weather_columns if col in day_data.columns]

    # 确保按小时排序
    day_data = day_data.sort_values('hour')

    # 创建小时索引映射
    hour_data_map = {}
    for _, row in day_data.iterrows():
        hour_data_map[row['hour']] = row[available_columns]

    hourly_97_points = []

    # 处理0-22点（92个点）
    for hour in range(23):
        if hour in hour_data_map and (hour + 1) in hour_data_map:
            current_data = hour_data_map[hour]
            next_data = hour_data_map[hour + 1]

            # 生成4个15分钟间隔点
            for minute_idx in range(4):
                ratio = minute_idx / 4.0
                interpolated_data = current_data + (next_data - current_data) * ratio

                time_str = f"{hour:02d}:{minute_idx*15:02d}"
                hourly_97_points.append({
                    'time': time_str,
                    'data': interpolated_data.to_dict()
                })
        else:
            # 如果缺少数据，填充None
            for minute_idx in range(4):
                time_str = f"{hour:02d}:{minute_idx*15:02d}"
                hourly_97_points.append({
                    'time': time_str,
                    'data': {col: None for col in available_columns}
                })

    # 处理23点（4个点 + 2400点）
    if 23 in hour_data_map:
        hour_23_data = hour_data_map[23]

        # 使用第二天00点数据进行插值
        if next_day_00_data is not None and not next_day_00_data.empty:
            next_day_00_values = next_day_00_data[available_columns]
            print(f"    ✅ 使用第二天00点数据进行23点插值")
        else:
            # 如果没有第二天数据，使用23点数据
            next_day_00_values = hour_23_data
            print(f"    ⚠️  缺少第二天00点数据，使用23点数据")

        # 23点的4个15分钟点
        for minute_idx in range(4):
            ratio = minute_idx / 4.0
            interpolated_data = hour_23_data + (next_day_00_values - hour_23_data) * ratio

            time_str = f"23:{minute_idx*15:02d}"
            hourly_97_points.append({
                'time': time_str,
                'data': interpolated_data.to_dict()
            })

        # 添加2400点（第二天00点数据）
        hourly_97_points.append({
            'time': "24:00",
            'data': next_day_00_values.to_dict()
        })
    else:
        # 如果没有23点数据，填充None
        for minute_idx in range(4):
            time_str = f"23:{minute_idx*15:02d}"
            hourly_97_points.append({
                'time': time_str,
                'data': {col: None for col in available_columns}
            })

        hourly_97_points.append({
            'time': "24:00",
            'data': {col: None for col in available_columns}
        })

    print(f"    📊 生成 {len(hourly_97_points)} 个时间点")

    return {
        'date': date,
        'points': hourly_97_points
    }

def export_results(results, city_name, start_date, end_date, weather_indicator=None):
    """导出结果到Excel文件"""
    if not results:
        return None

    # 准备导出数据
    export_data = []

    for day_result in results:
        date = day_result['date']
        for point in day_result['points']:
            row = {
                '城市': city_name,
                '日期': date,
                '时间': point['time']
            }
            # 添加气象数据
            row.update(point['data'])
            export_data.append(row)

    # 创建DataFrame
    df = pd.DataFrame(export_data)

    # 生成文件名
    if weather_indicator:
        output_file = f"{city_name}_{start_date}_{end_date}_{weather_indicator}_97points.xlsx"
    else:
        output_file = f"{city_name}_{start_date}_{end_date}_97points.xlsx"

    output_path = os.path.join("", output_file)

    # 导出为Excel文件
    try:
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 主数据表
            df.to_excel(writer, sheet_name='气象数据', index=False)

            # 创建汇总表
            if not df.empty:
                summary_data = []

                # 按日期汇总
                for date in df['日期'].unique():
                    date_data = df[df['日期'] == date]

                    summary_row = {
                        '日期': date,
                        '数据点数': len(date_data),
                        '城市': city_name
                    }

                    # 添加各指标的统计信息
                    numeric_columns = date_data.select_dtypes(include=[np.number]).columns
                    for col in numeric_columns:
                        if col in date_data.columns:
                            summary_row[f'{col}_平均值'] = round(date_data[col].mean(), 2)
                            summary_row[f'{col}_最大值'] = round(date_data[col].max(), 2)
                            summary_row[f'{col}_最小值'] = round(date_data[col].min(), 2)

                    summary_data.append(summary_row)

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='数据汇总', index=False)

                # 如果指定了单一指标，创建专门的图表数据表
                if weather_indicator and weather_indicator in df.columns:
                    chart_data = df[['日期', '时间', weather_indicator]].copy()
                    chart_data.to_excel(writer, sheet_name=f'{weather_indicator}数据', index=False)

        print(f"  📄 Excel文件已导出: {output_path}")
        print(f"  📊 导出数据形状: {df.shape}")

        # 显示Excel文件内容概览
        print(f"  📋 Excel工作表:")
        print(f"    - 气象数据: {df.shape[0]} 行 × {df.shape[1]} 列")
        if weather_indicator:
            print(f"    - {weather_indicator}数据: 专门的指标数据表")
        print(f"    - 数据汇总: 按日期汇总的统计信息")

    except Exception as e:
        print(f"  ❌ Excel导出失败: {e}")
        print(f"  🔄 尝试导出为CSV文件...")

        # 如果Excel导出失败，回退到CSV
        csv_output = output_path.replace('.xlsx', '.csv')
        df.to_csv(csv_output, index=False, encoding='utf-8-sig')
        print(f"  📄 CSV文件已导出: {csv_output}")
        return csv_output

    return output_path

def main():
    """主函数 - 支持命令行参数和交互式输入"""
    parser = argparse.ArgumentParser(description='吉林CMA实测气象数据解析器')
    parser.add_argument('--city', type=str, help='城市名称，如：长春市')
    parser.add_argument('--start', type=str, help='开始日期，格式：20250101')
    parser.add_argument('--end', type=str, help='结束日期，格式：20250102')
    parser.add_argument('--indicator', type=str, help='气象指标，如：tem(温度)、rhu(湿度)等，不指定则处理所有指标')
    parser.add_argument('--file', type=str, default='吉林cma实测气象.csv', help='CSV文件路径')

    args = parser.parse_args()

    # 可用的气象指标
    available_indicators = {
        'tem': '温度',
        'rhu': '相对湿度',
        'prs': '气压',
        'prs_sea': '海平面气压',
        'win_s_max': '最大风速',
        'win_d_s_max': '最大风速风向',
        'win_s_lnst_max': '瞬时最大风速',
        'win_d_lnst_max': '瞬时最大风速风向',
        'win_s_avg_2mi': '2分钟平均风速',
        'win_d_avg_2mi': '2分钟平均风向',
        'pre_1h': '1小时降水量',
        'vis': '能见度'
    }

    # 如果没有提供参数，使用交互式输入
    if not all([args.city, args.start, args.end]):
        print("🌤️  吉林CMA实测气象数据解析器")
        print("=" * 50)

        city_name = input("请输入城市名称（如：长春市）: ").strip()
        start_date = input("请输入开始日期（如：20250101）: ").strip()
        end_date = input("请输入结束日期（如：20250102）: ").strip()

        # 显示可用的气象指标
        print("\n📊 可用的气象指标:")
        print("=" * 30)
        for code, name in available_indicators.items():
            print(f"  {code}: {name}")

        print("\n💡 提示：")
        print("  - 直接回车：处理所有气象指标")
        print("  - 输入指标代码：只处理指定指标（如：tem）")

        weather_indicator = input("\n请输入气象指标代码（可选）: ").strip()
        weather_indicator = weather_indicator if weather_indicator else None

        if weather_indicator and weather_indicator not in available_indicators:
            print(f"⚠️  警告：指标 '{weather_indicator}' 不在可用列表中，将尝试处理")

    else:
        city_name = args.city
        start_date = args.start
        end_date = args.end
        weather_indicator = args.indicator
    
    # 检查CSV文件
    csv_file = os.path.join("", args.file)
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return

    # 执行解析
    results = parse_weather_data(csv_file, city_name, start_date, end_date, weather_indicator)

    if results:
        print(f"\n✅ 成功解析 {city_name} 的气象数据")
        print(f"📊 共处理 {len(results)} 天数据，每天97个时间点")
        if weather_indicator:
            print(f"🎯 专门处理气象指标: {weather_indicator}")
        else:
            print(f"📈 处理所有可用气象指标")
    else:
        print("❌ 解析失败")

if __name__ == "__main__":
    main()
