#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
吉林CMA实测气象数据交互式解析器
支持外部输入城市名称和日期范围，自动解析对应的地市气象数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
import argparse

def parse_weather_data(csv_file, city_name, start_date, end_date):
    """
    解析气象数据的主函数
    :param csv_file: CSV文件路径
    :param city_name: 城市名称
    :param start_date: 开始日期 (YYYYMMDD)
    :param end_date: 结束日期 (YYYYMMDD)
    """
    print(f"🌤️  开始解析 {city_name} 从 {start_date} 到 {end_date} 的气象数据")
    print("=" * 60)
    
    try:
        # 1. 加载数据
        print("📂 正在加载数据...")
        data = load_csv_data(csv_file)
        
        # 2. 数据预处理
        print("🔧 正在预处理数据...")
        processed_data = preprocess_data(data)
        
        # 3. 筛选城市和日期数据
        print(f"🏙️  正在筛选 {city_name} 的数据...")
        city_data = filter_city_data(processed_data, city_name, start_date, end_date)
        
        if city_data.empty:
            print(f"❌ 未找到 {city_name} 在指定日期范围内的数据")
            return None
        
        # 4. 按站点平均计算
        print("📊 正在计算站点平均值...")
        averaged_data = calculate_station_average(city_data)
        
        # 5. 转换为97点数据
        print("⏰ 正在转换为97点数据...")
        result_97_points = convert_to_97_points(averaged_data)
        
        # 6. 导出结果
        output_file = export_results(result_97_points, city_name, start_date, end_date)
        
        print("✅ 解析完成！")
        print(f"📄 结果文件: {output_file}")
        
        return result_97_points
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return None

def load_csv_data(csv_file):
    """加载CSV数据，自动检测编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    
    for encoding in encodings:
        try:
            data = pd.read_csv(csv_file, encoding=encoding)
            print(f"  ✅ 使用 {encoding} 编码成功加载")
            print(f"  📊 数据形状: {data.shape}")
            return data
        except UnicodeDecodeError:
            continue
    
    raise Exception("无法使用任何编码方式读取文件")

def preprocess_data(data):
    """数据预处理"""
    # 显示列名，帮助理解数据结构
    print(f"  📋 列名: {list(data.columns)}")
    
    # 这里需要根据实际CSV文件结构进行调整
    # 假设CSV包含以下列：站点名称、城市、日期、小时、温度等气象要素
    
    # 示例预处理步骤
    processed_data = data.copy()
    
    # 如果有日期时间列，转换格式
    date_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['date', '日期', '时间'])]
    for col in date_columns:
        try:
            processed_data[col] = pd.to_datetime(processed_data[col])
        except:
            pass
    
    return processed_data

def filter_city_data(data, city_name, start_date, end_date):
    """筛选指定城市和日期范围的数据"""
    # 转换日期格式
    start_dt = datetime.strptime(start_date, '%Y%m%d')
    end_dt = datetime.strptime(end_date, '%Y%m%d')
    
    filtered_data = data.copy()
    
    # 根据实际列名筛选城市数据
    city_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['city', '城市', '地市', '市'])]
    
    if city_columns:
        city_col = city_columns[0]
        print(f"  🏙️  使用列 '{city_col}' 筛选城市")
        filtered_data = filtered_data[filtered_data[city_col].str.contains(city_name, na=False)]
    else:
        print("  ⚠️  未找到城市列，将使用所有数据")
    
    # 根据日期筛选
    date_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['date', '日期'])]
    
    if date_columns:
        date_col = date_columns[0]
        print(f"  📅 使用列 '{date_col}' 筛选日期")
        try:
            filtered_data[date_col] = pd.to_datetime(filtered_data[date_col])
            filtered_data = filtered_data[
                (filtered_data[date_col] >= start_dt) & 
                (filtered_data[date_col] <= end_dt)
            ]
        except:
            print("  ⚠️  日期格式转换失败")
    
    print(f"  📊 筛选后数据形状: {filtered_data.shape}")
    return filtered_data

def calculate_station_average(city_data):
    """按站点平均计算地市气象数据"""
    # 识别数值列（气象要素）
    numeric_columns = city_data.select_dtypes(include=[np.number]).columns.tolist()
    print(f"  📊 数值列: {numeric_columns}")
    
    # 识别分组列
    group_columns = []
    
    # 查找日期列
    date_columns = [col for col in city_data.columns if any(keyword in col.lower() for keyword in ['date', '日期'])]
    if date_columns:
        group_columns.append(date_columns[0])
    
    # 查找小时列
    hour_columns = [col for col in city_data.columns if any(keyword in col.lower() for keyword in ['hour', '小时', '时'])]
    if hour_columns:
        group_columns.append(hour_columns[0])
    
    if group_columns and numeric_columns:
        print(f"  📊 按 {group_columns} 分组计算平均值")
        averaged_data = city_data.groupby(group_columns)[numeric_columns].mean().reset_index()
    else:
        print("  ⚠️  无法识别分组列，使用整体平均")
        averaged_data = city_data[numeric_columns].mean().to_frame().T
    
    return averaged_data

def convert_to_97_points(daily_data):
    """将24点数据转换为97点数据"""
    print("  ⏰ 开始24点到97点转换...")
    
    # 这里需要根据实际数据结构调整
    # 假设daily_data包含按日期和小时组织的数据
    
    result_data = []
    
    # 按日期分组处理
    if '日期' in daily_data.columns or 'date' in daily_data.columns:
        date_col = '日期' if '日期' in daily_data.columns else 'date'
        dates = daily_data[date_col].unique()
        
        for date in sorted(dates):
            day_data = daily_data[daily_data[date_col] == date]
            
            # 确保有24小时数据
            if len(day_data) >= 24:
                day_97_points = generate_97_points_for_day(day_data, date)
                result_data.append(day_97_points)
    
    print(f"  ✅ 转换完成，共 {len(result_data)} 天数据")
    return result_data

def generate_97_points_for_day(day_data, date):
    """为单天生成97个时间点的数据"""
    # 获取数值列
    numeric_columns = day_data.select_dtypes(include=[np.number]).columns.tolist()
    
    # 按小时排序
    hour_col = None
    for col in day_data.columns:
        if any(keyword in col.lower() for keyword in ['hour', '小时', '时']):
            hour_col = col
            break
    
    if hour_col:
        day_data = day_data.sort_values(hour_col)
    
    hourly_97_points = []
    
    # 处理0-22点
    for hour in range(23):
        if hour < len(day_data):
            current_data = day_data.iloc[hour][numeric_columns]
            next_data = day_data.iloc[hour + 1][numeric_columns] if hour + 1 < len(day_data) else current_data
            
            # 生成4个15分钟间隔点
            for minute_idx in range(4):
                ratio = minute_idx / 4.0
                interpolated_data = current_data + (next_data - current_data) * ratio
                
                time_str = f"{hour:02d}:{minute_idx*15:02d}"
                hourly_97_points.append({
                    'time': time_str,
                    'data': interpolated_data.to_dict()
                })
    
    # 处理23点（需要第二天00点数据进行插值）
    if len(day_data) >= 24:
        hour_23_data = day_data.iloc[23][numeric_columns]
        # 这里应该获取第二天00点数据，暂时使用当前数据
        next_day_00_data = hour_23_data  # 实际应用中需要获取第二天数据
        
        for minute_idx in range(4):
            ratio = minute_idx / 4.0
            interpolated_data = hour_23_data + (next_day_00_data - hour_23_data) * ratio
            
            time_str = f"23:{minute_idx*15:02d}"
            hourly_97_points.append({
                'time': time_str,
                'data': interpolated_data.to_dict()
            })
        
        # 添加2400点
        hourly_97_points.append({
            'time': "24:00",
            'data': next_day_00_data.to_dict()
        })
    
    return {
        'date': date,
        'points': hourly_97_points
    }

def export_results(results, city_name, start_date, end_date):
    """导出结果到CSV文件"""
    if not results:
        return None
    
    # 准备导出数据
    export_data = []
    
    for day_result in results:
        date = day_result['date']
        for point in day_result['points']:
            row = {
                '城市': city_name,
                '日期': date,
                '时间': point['time']
            }
            # 添加气象数据
            row.update(point['data'])
            export_data.append(row)
    
    # 创建DataFrame并导出
    df = pd.DataFrame(export_data)
    
    output_file = f"{city_name}_{start_date}_{end_date}_97points.csv"
    output_path = os.path.join("吉林cma气象数据解析", output_file)
    
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    
    print(f"  📄 数据已导出: {output_path}")
    print(f"  📊 导出数据形状: {df.shape}")
    
    return output_path

def main():
    """主函数 - 支持命令行参数和交互式输入"""
    parser = argparse.ArgumentParser(description='吉林CMA实测气象数据解析器')
    parser.add_argument('--city', type=str, help='城市名称，如：长春市')
    parser.add_argument('--start', type=str, help='开始日期，格式：20250101')
    parser.add_argument('--end', type=str, help='结束日期，格式：20250102')
    parser.add_argument('--file', type=str, default='吉林cma实测气象.csv', help='CSV文件路径')
    
    args = parser.parse_args()
    
    # 如果没有提供参数，使用交互式输入
    if not all([args.city, args.start, args.end]):
        print("🌤️  吉林CMA实测气象数据解析器")
        print("=" * 40)
        
        city_name = input("请输入城市名称（如：长春市）: ").strip()
        start_date = input("请输入开始日期（如：20250101）: ").strip()
        end_date = input("请输入结束日期（如：20250102）: ").strip()
    else:
        city_name = args.city
        start_date = args.start
        end_date = args.end
    
    # 检查CSV文件
    csv_file = os.path.join("", args.file)
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return
    
    # 执行解析
    results = parse_weather_data(csv_file, city_name, start_date, end_date)
    
    if results:
        print(f"\n✅ 成功解析 {city_name} 的气象数据")
        print(f"📊 共处理 {len(results)} 天数据，每天97个时间点")
    else:
        print("❌ 解析失败")

if __name__ == "__main__":
    main()
