# 吉林CMA实测气象数据解析器 - 横表SQL版使用说明

## 🆕 最新功能

### 1. 横表格式输出 📊
- **一天一行**：每天的数据在一行中显示
- **时间点为列**：t0000, t0015, t0030, ..., t2400 作为列头
- **97个时间点**：从00:00到24:00，15分钟间隔

### 2. SQL文件生成 🗄️
- **标准数据库格式**：适配 weather_city_his_basic 表结构
- **INSERT语句**：直接可用的SQL插入语句
- **气象类型映射**：1=湿度, 2=温度, 3=降雨量, 4=风速
- **城市ID支持**：支持指定城市ID

### 3. 输出格式选择 🎯
- **Excel横表**：适合数据分析和查看
- **SQL文件**：适合数据库导入

## 📊 支持的气象指标

| 指标代码 | 中文名称 | 单位 | SQL类型 | 说明 |
|----------|----------|------|---------|------|
| `tem` | 温度 | °C | type=2 | 空气温度 |
| `rhu` | 相对湿度 | % | type=1 | 空气相对湿度 |
| `pre_1h` | 1小时降水量 | mm | type=3 | 1小时累计降水量 |
| `win_s_avg_2mi` | 2分钟平均风速 | m/s | type=4 | 2分钟平均风速 |

## 🚀 使用方法

### 方法1：交互式使用（推荐）
```bash
cd "吉林cma气象数据解析"
python interactive_weather_parser.py
```

交互式输入示例：
```
请输入城市名称（如：长春市）: 长春市
请输入开始日期（如：20250101）: 20250101
请输入结束日期（如：20250102）: 20250102

📄 输出格式选择:
  1. Excel横表格式（一天一行，时间点为列）
  2. SQL插入语句（适用于数据库导入）
请选择输出格式 (1/2，默认1): 2

请输入城市ID（数字）: 2201

📊 可用的气象指标:
  tem: 温度(°C)
  rhu: 相对湿度(%)
  pre_1h: 1小时降水量(mm)
  win_s_avg_2mi: 2分钟平均风速(m/s)

请输入气象指标代码（可选）: tem
```

### 方法2：命令行参数

#### Excel横表格式
```bash
# 所有主要指标
python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --format excel

# 只处理温度
python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --format excel --indicator tem
```

#### SQL文件格式
```bash
# 所有主要指标
python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --format sql --city-id 2201

# 只处理温度
python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --format sql --city-id 2201 --indicator tem
```

### 方法3：Python代码调用
```python
from interactive_weather_parser import parse_weather_data

# Excel横表格式
results = parse_weather_data(
    csv_file="吉林cma实测气象.csv",
    city_name="长春市",
    start_date="20250101",
    end_date="20250102",
    weather_indicator=None,  # 所有指标
    city_id=None,
    output_format='excel'
)

# SQL文件格式
results = parse_weather_data(
    csv_file="吉林cma实测气象.csv",
    city_name="长春市",
    start_date="20250101",
    end_date="20250102",
    weather_indicator="tem",  # 只处理温度
    city_id="2201",
    output_format='sql'
)
```

## 📄 输出格式详解

### Excel横表格式
```
| 城市 | 日期 | 指标 | t0000 | t0015 | t0030 | ... | t2345 | t2400 |
|------|------|------|-------|-------|-------|-----|-------|-------|
| 长春市 | 2025-01-01 | tem | -15.2 | -15.1 | -15.0 | ... | -14.8 | -14.7 |
| 长春市 | 2025-01-01 | rhu | 75.0 | 75.2 | 75.4 | ... | 76.8 | 77.0 |
| 长春市 | 2025-01-02 | tem | -14.7 | -14.6 | -14.5 | ... | -14.3 | -14.2 |
```

### SQL文件格式
```sql
-- 吉林CMA气象数据插入SQL
-- 城市: 长春市 (ID: 2201)
-- 日期范围: 20250101 - 20250102
-- 气象类型: 1=湿度, 2=温度, 3=降雨量, 4=风速

INSERT INTO weather_city_his_basic (
    id, date, city_id, type,
    t0000, t0015, t0030, t0045, t0100, t0115, t0130, t0145,
    ...
    t2300, t2315, t2330, t2345, t2400
) VALUES (
    'uuid-string', '2025-01-01', '2201', 2,
    -15.20, -15.10, -15.00, -14.90, -14.80, -14.70, -14.60, -14.50,
    ...
    -14.80, -14.75, -14.70, -14.65, -14.60
);
```

## 🗄️ 数据库表结构

解析器适配的数据库表结构：
```sql
CREATE TABLE "weather_city_his_basic" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL,
  "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
  "t0000" decimal(32,2) DEFAULT NULL,
  "t0015" decimal(32,2) DEFAULT NULL,
  ...
  "t2400" decimal(32,2) DEFAULT NULL,
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "weather_city_his_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='实际气象表';
```

## 📁 文件命名规则

### Excel文件
- **所有指标**: `城市名_开始日期_结束日期_横表.xlsx`
- **单一指标**: `城市名_开始日期_结束日期_指标代码_横表.xlsx`

### SQL文件
- **所有指标**: `城市名_开始日期_结束日期_insert.sql`
- **单一指标**: `城市名_开始日期_结束日期_指标代码_insert.sql`

示例：
- `长春市_20250101_20250102_横表.xlsx`
- `长春市_20250101_20250102_tem_横表.xlsx`
- `长春市_20250101_20250102_insert.sql`
- `长春市_20250101_20250102_tem_insert.sql`

## 🎯 核心算法

### 横表转换
```python
# 时间点列名生成
time_columns = []
for hour in range(24):
    for minute in [0, 15, 30, 45]:
        time_columns.append(f"t{hour:02d}{minute:02d}")
time_columns.append("t2400")  # 97个时间点

# 横表数据组织
for day_result in results:
    for indicator in weather_indicators:
        row = {'城市': city_name, '日期': date, '指标': indicator}
        for i, time_col in enumerate(time_columns):
            row[time_col] = points[i]['data'].get(indicator)
```

### SQL生成
```python
# 气象类型映射
weather_type_map = {
    'rhu': 1,      # 湿度
    'tem': 2,      # 温度  
    'pre_1h': 3,   # 降雨量
    'win_s_avg_2mi': 4  # 风速
}

# INSERT语句生成
INSERT INTO weather_city_his_basic (
    id, date, city_id, type, t0000, t0015, ..., t2400
) VALUES (
    '{uuid}', '{date}', '{city_id}', {type}, {value1}, {value2}, ..., {value97}
);
```

## ⚠️ 注意事项

### 1. 城市ID要求
- **SQL格式必需**：生成SQL文件时必须提供城市ID
- **Excel格式可选**：Excel格式不需要城市ID

### 2. 数据精度
- **保持原始精度**：保持CSV文件中的原始数据精度
- **SQL格式**：decimal(32,2) 格式，保留2位小数

### 3. 时间点处理
- **97个时间点**：00:00-24:00，15分钟间隔
- **2400点特殊处理**：使用第二天00:00的数据

### 4. 气象指标限制
- **只处理4个主要指标**：温度、湿度、降雨、风速
- **其他指标忽略**：不处理其他气象要素

## 🔍 故障排除

### 问题1：SQL格式提示需要城市ID
**解决**：使用 `--city-id` 参数或在交互模式中输入城市ID

### 问题2：指标不在主要列表中
**解决**：只使用 tem、rhu、pre_1h、win_s_avg_2mi 四个指标

### 问题3：横表列数不对
**解决**：确保生成了97个时间点列（t0000到t2400）

### 问题4：SQL语句格式错误
**解决**：检查数据库表结构是否匹配

## 🎉 使用建议

1. **首次使用**：建议用交互式模式熟悉功能
2. **批量处理**：使用命令行参数进行批量处理
3. **数据验证**：生成后检查Excel或SQL文件的格式
4. **数据库导入**：SQL文件可直接在数据库中执行

现在您可以生成标准的横表格式Excel文件和可直接导入数据库的SQL文件了！
