#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试脚本：验证吉林CMA气象数据解析器
"""

import os
import sys
import pandas as pd
from datetime import datetime

def test_csv_file():
    """测试CSV文件是否存在和可读"""
    print("🧪 测试1: CSV文件检查")
    print("-" * 30)
    
    csv_file = "吉林cma实测气象.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return False
    
    print(f"✅ 文件存在: {csv_file}")
    
    # 尝试读取文件
    try:
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        data = None
        
        for encoding in encodings:
            try:
                data = pd.read_csv(csv_file, encoding=encoding)
                print(f"✅ 成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if data is None:
            print("❌ 无法读取文件（编码问题）")
            return False
        
        print(f"📊 数据形状: {data.shape}")
        print(f"📋 列名: {list(data.columns)}")
        
        # 显示前几行数据
        print("\n📄 前5行数据:")
        print(data.head())
        
        # 分析数据结构
        print(f"\n🔍 数据类型:")
        print(data.dtypes)
        
        return True, data
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def analyze_data_structure(data):
    """分析数据结构"""
    print("\n🧪 测试2: 数据结构分析")
    print("-" * 30)
    
    columns = list(data.columns)
    
    # 查找关键列
    key_columns = {
        '城市相关': [],
        '日期相关': [],
        '时间相关': [],
        '数值相关': []
    }
    
    for col in columns:
        col_lower = col.lower()
        
        # 城市相关列
        if any(keyword in col_lower for keyword in ['city', '城市', '地市', '市', 'station', '站点']):
            key_columns['城市相关'].append(col)
        
        # 日期相关列
        elif any(keyword in col_lower for keyword in ['date', '日期', 'time', '时间']):
            key_columns['日期相关'].append(col)
        
        # 时间相关列
        elif any(keyword in col_lower for keyword in ['hour', '小时', '时']):
            key_columns['时间相关'].append(col)
    
    # 数值列
    numeric_columns = data.select_dtypes(include=['number']).columns.tolist()
    key_columns['数值相关'] = numeric_columns
    
    print("🔍 关键列识别:")
    for category, cols in key_columns.items():
        if cols:
            print(f"  {category}: {cols}")
        else:
            print(f"  {category}: 未找到")
    
    return key_columns

def test_city_filtering(data):
    """测试城市筛选功能"""
    print("\n🧪 测试3: 城市筛选测试")
    print("-" * 30)
    
    # 查找城市列
    city_columns = []
    for col in data.columns:
        if any(keyword in col.lower() for keyword in ['city', '城市', '地市', '市']):
            city_columns.append(col)
    
    if not city_columns:
        print("⚠️  未找到城市列，尝试查看所有列的唯一值")
        for col in data.columns:
            unique_values = data[col].unique()
            if len(unique_values) < 20:  # 假设城市数量不会太多
                print(f"  {col}: {unique_values}")
        return
    
    city_col = city_columns[0]
    print(f"🏙️  使用城市列: {city_col}")
    
    # 显示所有城市
    cities = data[city_col].unique()
    print(f"📍 发现城市: {cities}")
    
    # 测试长春市数据
    test_city = "长春市"
    city_data = data[data[city_col].str.contains(test_city, na=False)]
    
    if not city_data.empty:
        print(f"✅ 找到 {test_city} 数据: {city_data.shape[0]} 条记录")
    else:
        print(f"❌ 未找到 {test_city} 数据")
        
        # 尝试模糊匹配
        for city in cities:
            if "长春" in str(city):
                print(f"💡 建议使用: {city}")

def test_date_filtering(data):
    """测试日期筛选功能"""
    print("\n🧪 测试4: 日期筛选测试")
    print("-" * 30)
    
    # 查找日期列
    date_columns = []
    for col in data.columns:
        if any(keyword in col.lower() for keyword in ['date', '日期']):
            date_columns.append(col)
    
    if not date_columns:
        print("⚠️  未找到明确的日期列")
        # 尝试查找可能的日期列
        for col in data.columns:
            sample_values = data[col].dropna().head(5).tolist()
            print(f"  {col} 示例值: {sample_values}")
        return
    
    date_col = date_columns[0]
    print(f"📅 使用日期列: {date_col}")
    
    # 显示日期范围
    try:
        data[date_col] = pd.to_datetime(data[date_col])
        min_date = data[date_col].min()
        max_date = data[date_col].max()
        print(f"📊 日期范围: {min_date} 到 {max_date}")
        
        # 显示日期分布
        date_counts = data[date_col].value_counts().head(10)
        print(f"📈 日期分布（前10）:")
        for date, count in date_counts.items():
            print(f"  {date.strftime('%Y-%m-%d')}: {count} 条记录")
            
    except Exception as e:
        print(f"❌ 日期格式转换失败: {e}")
        sample_dates = data[date_col].dropna().head(10).tolist()
        print(f"📄 日期样本: {sample_dates}")

def create_sample_data():
    """创建示例数据用于测试"""
    print("\n🧪 测试5: 创建示例数据")
    print("-" * 30)
    
    # 创建示例数据
    import numpy as np
    from datetime import datetime, timedelta
    
    dates = [datetime(2025, 1, 1) + timedelta(days=i) for i in range(2)]
    hours = list(range(24))
    cities = ["长春市", "吉林市"]
    stations = ["站点1", "站点2", "站点3"]
    
    sample_data = []
    
    for date in dates:
        for hour in hours:
            for city in cities:
                for station in stations:
                    # 生成模拟气象数据
                    temp = -10 + np.random.normal(0, 5)  # 温度
                    humidity = 60 + np.random.normal(0, 10)  # 湿度
                    wind_speed = 3 + np.random.normal(0, 2)  # 风速
                    
                    sample_data.append({
                        '城市': city,
                        '站点': station,
                        '日期': date.strftime('%Y-%m-%d'),
                        '小时': hour,
                        '温度': round(temp, 1),
                        '湿度': round(humidity, 1),
                        '风速': round(wind_speed, 1)
                    })
    
    df = pd.DataFrame(sample_data)
    
    # 保存示例数据
    sample_file = "示例气象数据.csv"
    df.to_csv(sample_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建示例数据: {sample_file}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    
    return sample_file

def test_parser_with_sample():
    """使用示例数据测试解析器"""
    print("\n🧪 测试6: 解析器功能测试")
    print("-" * 30)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        sample_file = "示例气象数据.csv"
        
        if not os.path.exists(sample_file):
            sample_file = create_sample_data()
        
        # 测试解析功能
        results = parse_weather_data(
            csv_file=sample_file,
            city_name="长春市",
            start_date="20250101",
            end_date="20250102"
        )
        
        if results:
            print("✅ 解析器测试成功")
            print(f"📊 结果: {len(results)} 天数据")
            
            # 显示第一天的部分结果
            if results:
                first_day = results[0]
                print(f"📅 第一天日期: {first_day['date']}")
                print(f"⏰ 时间点数量: {len(first_day['points'])}")
                
                print("🕐 前10个时间点:")
                for i, point in enumerate(first_day['points'][:10]):
                    print(f"  {point['time']}: {point['data']}")
        else:
            print("❌ 解析器测试失败")
            
    except ImportError as e:
        print(f"❌ 导入解析器失败: {e}")
    except Exception as e:
        print(f"❌ 解析器测试出错: {e}")

def main():
    """主测试函数"""
    print("🧪 吉林CMA气象数据解析器测试")
    print("=" * 50)
    
    # 切换到正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 测试1: 检查CSV文件
    result = test_csv_file()
    if isinstance(result, tuple) and result[0]:
        data = result[1]
        
        # 测试2: 分析数据结构
        analyze_data_structure(data)
        
        # 测试3: 城市筛选
        test_city_filtering(data)
        
        # 测试4: 日期筛选
        test_date_filtering(data)
    
    # 测试5&6: 创建示例数据并测试解析器
    test_parser_with_sample()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
