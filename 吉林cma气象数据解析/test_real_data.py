#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试真实数据格式的解析器
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_real_format_sample():
    """创建符合真实数据格式的示例数据"""
    print("📊 创建真实格式示例数据...")
    
    # 真实的列名
    columns = [
        "station_id", "station_name", "station_type", "province", "city", "district", 
        "lon", "lat", "altitude", "pressure_altitude", "adcode", "note", "update_time", 
        "station_id", "time", "prs", "prs_sea", "win_s_max", "win_d_s_max", 
        "win_s_lnst_max", "win_d_lnst_max", "win_s_avg_2mi", "win_d_avg_2mi", 
        "tem", "rhu", "pre_1h", "vis", "update_time"
    ]
    
    # 生成示例数据
    sample_data = []
    
    # 城市和站点配置
    cities_stations = {
        "长春市": ["长春站1", "长春站2", "长春站3"],
        "吉林市": ["吉林站1", "吉林站2"],
        "延边朝鲜族自治州": ["延边站1", "延边站2", "敦化市"]
    }
    
    # 生成2天的数据
    base_date = datetime(2025, 1, 1)
    
    for day_offset in range(2):
        current_date = base_date + timedelta(days=day_offset)
        
        for hour in range(24):
            current_time = current_date + timedelta(hours=hour)
            time_str = current_time.strftime("%Y/%m/%d %H:%M:%S")
            
            for city, stations in cities_stations.items():
                for i, station in enumerate(stations):
                    # 生成模拟气象数据
                    base_temp = -15 + hour * 0.8 + np.random.normal(0, 2)
                    
                    row = [
                        f"5418{i+1}",  # station_id
                        station,       # station_name
                        "A",          # station_type
                        "吉林省",      # province
                        city,         # city
                        city,         # district
                        "128.2",      # lon
                        "43.37",      # lat
                        "526",        # altitude
                        "526",        # pressure_altitude
                        "222403",     # adcode
                        "",           # note
                        "2024/11/19 18:13:46",  # update_time
                        f"5418{i+1}",  # station_id (重复)
                        time_str,     # time
                        round(960 + np.random.normal(0, 5), 1),      # prs
                        round(1028 + np.random.normal(0, 5), 1),     # prs_sea
                        round(1.5 + np.random.normal(0, 0.5), 1),    # win_s_max
                        round(212 + np.random.normal(0, 20), 0),     # win_d_s_max
                        round(2.9 + np.random.normal(0, 0.5), 1),    # win_s_lnst_max
                        round(260 + np.random.normal(0, 20), 0),     # win_d_lnst_max
                        round(1.6 + np.random.normal(0, 0.3), 1),    # win_s_avg_2mi
                        round(264 + np.random.normal(0, 15), 0),     # win_d_avg_2mi
                        round(base_temp, 1),                         # tem (温度)
                        round(83 + np.random.normal(0, 10), 0),      # rhu (湿度)
                        round(np.random.exponential(0.1), 1),        # pre_1h (降水)
                        "999999",     # vis
                        "2025/4/16 19:50:20"  # update_time
                    ]
                    
                    sample_data.append(row)
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data, columns=columns)
    
    # 保存到CSV文件
    output_file = "吉林cma实测气象.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 真实格式示例数据已保存: {output_file}")
    print(f"📊 数据形状: {df.shape}")
    print(f"🏙️  包含城市: {df['city'].unique()}")
    print(f"📅 时间范围: {df['time'].min()} 到 {df['time'].max()}")
    
    # 显示数据样本
    print(f"\n📄 数据样本:")
    print(df[['city', 'station_name', 'time', 'tem', 'rhu', 'prs']].head(10))
    
    return output_file

def test_real_data_parsing():
    """测试真实数据格式的解析"""
    print("\n" + "="*60)
    print("🧪 测试真实数据格式解析")
    print("="*60)
    
    # 确保有测试数据
    csv_file = "吉林cma实测气象.csv"
    if not os.path.exists(csv_file):
        csv_file = create_real_format_sample()
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        # 测试长春市数据解析
        print("\n🏙️  测试长春市数据解析...")
        results = parse_weather_data(
            csv_file=csv_file,
            city_name="长春市",
            start_date="20250101",
            end_date="20250102"
        )
        
        if results:
            print("✅ 长春市解析成功")
            print(f"📊 结果: {len(results)} 天数据")
            
            # 显示第一天的部分结果
            if results:
                first_day = results[0]
                print(f"📅 第一天: {first_day['date']}")
                print(f"⏰ 时间点数量: {len(first_day['points'])}")
                
                print("\n🌡️  温度数据前10个时间点:")
                for i, point in enumerate(first_day['points'][:10]):
                    tem_value = point['data'].get('tem', 'N/A')
                    print(f"  {point['time']}: {tem_value}°C")
                
                print(f"\n🌡️  23点到24点温度数据:")
                for point in first_day['points'][-5:]:
                    tem_value = point['data'].get('tem', 'N/A')
                    print(f"  {point['time']}: {tem_value}°C")
        
        # 测试延边朝鲜族自治州数据解析
        print(f"\n🏙️  测试延边朝鲜族自治州数据解析...")
        results2 = parse_weather_data(
            csv_file=csv_file,
            city_name="延边朝鲜族自治州",
            start_date="20250101",
            end_date="20250101"
        )
        
        if results2:
            print("✅ 延边朝鲜族自治州解析成功")
            print(f"📊 结果: {len(results2)} 天数据")
        else:
            print("❌ 延边朝鲜族自治州解析失败")
            
    except Exception as e:
        print(f"❌ 解析测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_data_structure():
    """测试数据结构识别"""
    print("\n🔍 测试数据结构识别...")
    
    csv_file = "吉林cma实测气象.csv"
    if not os.path.exists(csv_file):
        csv_file = create_real_format_sample()
    
    try:
        # 加载数据
        data = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"📊 数据形状: {data.shape}")
        print(f"📋 列名: {list(data.columns)}")
        
        # 检查关键列
        print(f"\n🏙️  城市列数据:")
        if 'city' in data.columns:
            print(f"  唯一城市: {data['city'].unique()}")
            print(f"  城市数据量: {data['city'].value_counts()}")
        
        print(f"\n📅 时间列数据:")
        if 'time' in data.columns:
            print(f"  时间格式样本: {data['time'].head().tolist()}")
            
            # 转换时间格式
            data['time'] = pd.to_datetime(data['time'])
            print(f"  时间范围: {data['time'].min()} 到 {data['time'].max()}")
            
            # 按日期统计
            data['date'] = data['time'].dt.date
            print(f"  日期分布: {data['date'].value_counts()}")
        
        print(f"\n🌡️  气象要素数据:")
        weather_cols = ['tem', 'rhu', 'prs', 'win_s_avg_2mi']
        for col in weather_cols:
            if col in data.columns:
                print(f"  {col}: 范围 {data[col].min()} - {data[col].max()}, 平均 {data[col].mean():.2f}")
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 真实数据格式解析器测试")
    print("="*60)
    
    # 切换到正确目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 测试1: 创建真实格式数据
    create_real_format_sample()
    
    # 测试2: 数据结构识别
    test_data_structure()
    
    # 测试3: 解析功能
    test_real_data_parsing()
    
    print(f"\n" + "="*60)
    print("🏁 测试完成")
    print("💡 现在可以使用真实的CSV文件进行解析了")

if __name__ == "__main__":
    main()
