# 吉林CMA实测气象数据解析器

## 功能说明

本解析器专门用于处理吉林省CMA实测气象数据，主要功能包括：

1. **站点数据聚合**：将各地市多个站点的气象数据按站点平均方式计算出地市级气象数据
2. **时间插值**：将24小时数据转换为97个时间点（15分钟间隔）
3. **边界处理**：2400点使用第二天0000点数据，与23点进行线性插值
4. **灵活输入**：支持外部输入城市名称和日期范围

## 文件结构

```
吉林cma气象数据解析/
├── 吉林cma实测气象.csv          # 原始数据文件
├── jilin_weather_parser.py      # 核心解析器类
├── interactive_weather_parser.py # 交互式解析器
├── README.md                    # 说明文档
└── 输出文件/
    └── [城市名]_[开始日期]_[结束日期]_97points.csv
```

## 使用方法

### 方法1：交互式使用

```bash
cd "吉林cma气象数据解析"
python interactive_weather_parser.py
```

然后按提示输入：
- 城市名称：如 `长春市`
- 开始日期：如 `20250101`
- 结束日期：如 `20250102`

### 方法2：命令行参数

```bash
python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102
```

### 方法3：Python代码调用

```python
from interactive_weather_parser import parse_weather_data

# 解析长春市2025年1月1日到1月2日的数据
results = parse_weather_data(
    csv_file="吉林cma实测气象.csv",
    city_name="长春市", 
    start_date="20250101",
    end_date="20250102"
)
```

## 输出格式

解析后的数据将保存为CSV文件，包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 城市 | 城市名称 | 长春市 |
| 日期 | 日期 | 2025-01-01 |
| 时间 | 时间点（15分钟间隔） | 00:00, 00:15, 00:30, 00:45, 01:00... |
| 温度 | 温度数据 | -10.5 |
| 湿度 | 湿度数据 | 65.2 |
| ... | 其他气象要素 | ... |

## 时间点说明

每天生成97个时间点：
- **00:00 - 22:45**：92个点（23小时 × 4个15分钟间隔）
- **23:00 - 23:45**：4个点（23点与第二天00点插值）
- **24:00**：1个点（第二天00:00的数据）

## 插值算法

### 常规小时插值（0-22点）
```
对于每小时内的4个15分钟点：
- 00分：当前小时原始值
- 15分：当前小时值 + (下一小时值 - 当前小时值) × 0.25
- 30分：当前小时值 + (下一小时值 - 当前小时值) × 0.50
- 45分：当前小时值 + (下一小时值 - 当前小时值) × 0.75
```

### 23点特殊处理
```
23点与第二天00点之间插值：
- 23:00：23点原始值
- 23:15：23点值 + (第二天00点值 - 23点值) × 0.25
- 23:30：23点值 + (第二天00点值 - 23点值) × 0.50
- 23:45：23点值 + (第二天00点值 - 23点值) × 0.75
- 24:00：第二天00点原始值
```

## 支持的城市

- 长春市
- 吉林市
- 四平市
- 辽源市
- 通化市
- 白山市
- 松原市
- 白城市
- 延边朝鲜族自治州

## 数据要求

CSV文件应包含以下列（列名可能有所不同）：
- 城市/地市列：用于筛选城市数据
- 日期列：用于筛选日期范围
- 小时列：用于识别24小时数据
- 气象要素列：温度、湿度等数值数据

## 注意事项

1. **编码支持**：自动检测CSV文件编码（UTF-8、GBK、GB2312等）
2. **数据缺失**：如果某个时间点数据缺失，将在输出中标记为空值
3. **站点平均**：多个站点的数据会自动计算平均值
4. **日期格式**：输入日期格式为YYYYMMDD（如20250101）

## 错误处理

- 文件不存在：检查CSV文件路径
- 城市未找到：检查城市名称是否正确
- 日期格式错误：确保使用YYYYMMDD格式
- 数据列缺失：检查CSV文件是否包含必要的列

## 示例输出

```csv
城市,日期,时间,温度,湿度,风速
长春市,2025-01-01,00:00,-12.5,68.2,3.2
长春市,2025-01-01,00:15,-12.3,68.5,3.1
长春市,2025-01-01,00:30,-12.1,68.8,3.0
...
长春市,2025-01-01,23:45,-15.2,72.1,2.8
长春市,2025-01-01,24:00,-15.5,72.5,2.7
```

## 技术支持

如果遇到问题，请检查：
1. CSV文件格式是否正确
2. 城市名称是否在支持列表中
3. 日期范围是否有数据
4. Python环境是否安装了必要的依赖包（pandas, numpy）
