#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试解析器
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_sample_data():
    """创建符合真实格式的示例数据"""
    print("创建示例数据...")
    
    # 真实列名
    columns = [
        "station_id", "station_name", "station_type", "province", "city", "district", 
        "lon", "lat", "altitude", "pressure_altitude", "adcode", "note", "update_time", 
        "station_id", "time", "prs", "prs_sea", "win_s_max", "win_d_s_max", 
        "win_s_lnst_max", "win_d_lnst_max", "win_s_avg_2mi", "win_d_avg_2mi", 
        "tem", "rhu", "pre_1h", "vis", "update_time"
    ]
    
    data = []
    
    # 生成长春市2天数据
    for day in range(2):
        for hour in range(24):
            for station in range(3):  # 3个站点
                time_str = f"2025/1/{day+1} {hour:02d}:00:00"
                
                row = [
                    f"5418{station+1}", "长春站" + str(station+1), "A", "吉林省", "长春市", "长春市",
                    "125.3", "43.8", "200", "200", "220100", "", "2024/11/19 18:13:46",
                    f"5418{station+1}", time_str,
                    960.0 + np.random.normal(0, 2),  # prs
                    1028.0 + np.random.normal(0, 2), # prs_sea
                    1.5 + np.random.normal(0, 0.5),  # win_s_max
                    212, 2.9, 260, 1.6, 264,
                    -15.0 + hour * 0.5 + np.random.normal(0, 1),  # tem (温度)
                    80 + np.random.normal(0, 5),     # rhu (湿度)
                    0.0, "999999", "2025/4/16 19:50:20"
                ]
                data.append(row)
    
    df = pd.DataFrame(data, columns=columns)
    df.to_csv("吉林cma实测气象.csv", index=False, encoding='utf-8-sig')
    print(f"示例数据已创建: {df.shape}")
    return df

def test_parsing():
    """测试解析功能"""
    print("\n开始测试解析...")
    
    # 创建示例数据
    df = create_sample_data()
    
    # 手动执行解析步骤
    print("\n1. 筛选长春市数据...")
    city_data = df[df['city'] == '长春市'].copy()
    print(f"长春市数据: {city_data.shape[0]} 条")
    
    print("\n2. 筛选日期数据...")
    city_data['time'] = pd.to_datetime(city_data['time'])
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 2)
    
    date_filtered = city_data[
        (city_data['time'].dt.date >= start_date.date()) & 
        (city_data['time'].dt.date <= end_date.date())
    ]
    print(f"日期筛选后: {date_filtered.shape[0]} 条")
    
    print("\n3. 计算站点平均...")
    date_filtered['date'] = date_filtered['time'].dt.date
    date_filtered['hour'] = date_filtered['time'].dt.hour
    
    weather_cols = ['tem', 'rhu', 'prs']
    averaged = date_filtered.groupby(['date', 'hour'])[weather_cols].mean().reset_index()
    print(f"平均后数据: {averaged.shape[0]} 条")
    print("前5条数据:")
    print(averaged.head())
    
    print("\n4. 转换为97点...")
    dates = sorted(averaged['date'].unique())
    
    for date in dates:
        day_data = averaged[averaged['date'] == date].sort_values('hour')
        print(f"\n处理日期: {date}")
        print(f"小时数据: {len(day_data)} 条")
        
        # 生成前10个时间点作为示例
        points = []
        for hour in range(min(3, len(day_data))):  # 只处理前3小时作为示例
            if hour < len(day_data) - 1:
                current = day_data.iloc[hour]
                next_hour = day_data.iloc[hour + 1]
                
                for minute_idx in range(4):
                    ratio = minute_idx / 4.0
                    interpolated_tem = current['tem'] + (next_hour['tem'] - current['tem']) * ratio
                    
                    time_str = f"{hour:02d}:{minute_idx*15:02d}"
                    points.append(f"{time_str}: {interpolated_tem:.1f}°C")
        
        print("前12个时间点:")
        for point in points:
            print(f"  {point}")
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    test_parsing()
