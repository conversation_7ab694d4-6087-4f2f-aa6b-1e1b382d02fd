#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
吉林CMA实测气象数据解析器
功能：
1. 解析吉林各地市站点气象数据
2. 按站点平均计算地市气象数据
3. 24点数据转换为97点数据（15分钟间隔）
4. 2400点使用第二天0000点数据，与23点进行插值
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

class JilinWeatherParser:
    def __init__(self, csv_file_path):
        """
        初始化解析器
        :param csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.data = None
        self.city_station_mapping = {}
        
        # 吉林省地市列表
        self.jilin_cities = [
            '长春市', '吉林市', '四平市', '辽源市', '通化市', 
            '白山市', '松原市', '白城市', '延边朝鲜族自治州'
        ]
        
        self.load_data()
    
    def load_data(self):
        """加载CSV数据"""
        try:
            print(f"正在加载数据文件: {self.csv_file_path}")
            
            # 尝试不同的编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings:
                try:
                    self.data = pd.read_csv(self.csv_file_path, encoding=encoding)
                    print(f"✅ 成功使用 {encoding} 编码加载数据")
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.data is None:
                raise Exception("无法使用任何编码方式读取文件")
            
            print(f"数据形状: {self.data.shape}")
            print(f"列名: {list(self.data.columns)}")
            print("\n前5行数据:")
            print(self.data.head())
            
            self._analyze_data_structure()
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            sys.exit(1)
    
    def _analyze_data_structure(self):
        """分析数据结构"""
        print("\n" + "="*50)
        print("数据结构分析")
        print("="*50)
        
        # 分析列名
        columns = list(self.data.columns)
        print(f"总列数: {len(columns)}")
        
        # 查找可能的关键列
        key_columns = {
            'station': [],
            'city': [],
            'date': [],
            'time': [],
            'temperature': []
        }
        
        for col in columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['station', '站点', '站名']):
                key_columns['station'].append(col)
            elif any(keyword in col_lower for keyword in ['city', '城市', '地市']):
                key_columns['city'].append(col)
            elif any(keyword in col_lower for keyword in ['date', '日期', '时间']):
                key_columns['date'].append(col)
            elif any(keyword in col_lower for keyword in ['temp', '温度', '气温']):
                key_columns['temperature'].append(col)
        
        print("关键列识别:")
        for key, cols in key_columns.items():
            if cols:
                print(f"  {key}: {cols}")
        
        # 显示数据类型
        print(f"\n数据类型:")
        print(self.data.dtypes)
    
    def parse_city_weather(self, city_name, start_date, end_date):
        """
        解析指定城市的气象数据
        :param city_name: 城市名称，如"长春市"
        :param start_date: 开始日期，格式"20250101"
        :param end_date: 结束日期，格式"20250102"
        :return: 解析后的气象数据
        """
        print(f"\n开始解析 {city_name} 从 {start_date} 到 {end_date} 的气象数据")
        
        # 转换日期格式
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')
        
        # 筛选城市数据（这里需要根据实际CSV结构调整）
        city_data = self._filter_city_data(city_name, start_dt, end_dt)
        
        if city_data.empty:
            print(f"❌ 未找到 {city_name} 的数据")
            return None
        
        # 按站点平均计算地市数据
        averaged_data = self._calculate_city_average(city_data)
        
        # 转换为97点数据
        result_data = self._convert_to_97_points(averaged_data)
        
        return result_data
    
    def _filter_city_data(self, city_name, start_dt, end_dt):
        """
        筛选指定城市和日期范围的数据
        注意：这个方法需要根据实际CSV文件结构进行调整
        """
        # 示例实现，需要根据实际数据结构调整
        filtered_data = self.data.copy()
        
        # 假设有城市列
        if '城市' in self.data.columns:
            filtered_data = filtered_data[filtered_data['城市'] == city_name]
        elif '地市' in self.data.columns:
            filtered_data = filtered_data[filtered_data['地市'] == city_name]
        
        # 假设有日期列
        if '日期' in self.data.columns:
            # 转换日期格式并筛选
            filtered_data['日期'] = pd.to_datetime(filtered_data['日期'])
            filtered_data = filtered_data[
                (filtered_data['日期'] >= start_dt) & 
                (filtered_data['日期'] <= end_dt)
            ]
        
        return filtered_data
    
    def _calculate_city_average(self, city_data):
        """
        按站点平均计算地市气象数据
        """
        print("正在计算站点平均值...")
        
        # 识别数值列（温度、湿度等气象要素）
        numeric_columns = city_data.select_dtypes(include=[np.number]).columns.tolist()
        
        # 按日期和小时分组，计算平均值
        if '日期' in city_data.columns and '小时' in city_data.columns:
            grouped = city_data.groupby(['日期', '小时'])[numeric_columns].mean()
        else:
            # 如果没有明确的日期和小时列，需要根据实际情况调整
            grouped = city_data[numeric_columns].mean()
        
        return grouped
    
    def _convert_to_97_points(self, daily_data):
        """
        将24点数据转换为97点数据（15分钟间隔）
        2400点使用第二天0000点数据，与23点进行插值
        """
        print("正在转换为97点数据...")
        
        result_data = []
        
        # 假设daily_data是按日期组织的24小时数据
        dates = sorted(daily_data.index.get_level_values(0).unique()) if hasattr(daily_data.index, 'get_level_values') else [daily_data.index[0]]
        
        for i, date in enumerate(dates):
            daily_97_points = []
            
            # 获取当天24小时数据
            if hasattr(daily_data.index, 'get_level_values'):
                day_data = daily_data.loc[date]
            else:
                day_data = daily_data
            
            # 处理0-22点：每小时内插值生成4个15分钟点
            for hour in range(23):
                current_hour_data = day_data.iloc[hour] if len(day_data) > hour else None
                next_hour_data = day_data.iloc[hour + 1] if len(day_data) > hour + 1 else None
                
                if current_hour_data is not None and next_hour_data is not None:
                    # 线性插值生成4个15分钟点
                    for minute_idx in range(4):
                        ratio = minute_idx / 4.0  # 0, 0.25, 0.5, 0.75
                        interpolated = current_hour_data + (next_hour_data - current_hour_data) * ratio
                        daily_97_points.append({
                            'time': f"{hour:02d}:{minute_idx*15:02d}",
                            'data': interpolated
                        })
                else:
                    # 如果数据缺失，填充None
                    for minute_idx in range(4):
                        daily_97_points.append({
                            'time': f"{hour:02d}:{minute_idx*15:02d}",
                            'data': None
                        })
            
            # 处理23点：与第二天00点插值
            hour_23_data = day_data.iloc[23] if len(day_data) > 23 else None
            next_day_00_data = None
            
            # 获取第二天00点数据
            if i + 1 < len(dates):
                next_date = dates[i + 1]
                if hasattr(daily_data.index, 'get_level_values'):
                    next_day_data = daily_data.loc[next_date]
                    next_day_00_data = next_day_data.iloc[0] if len(next_day_data) > 0 else None
            
            if hour_23_data is not None and next_day_00_data is not None:
                # 23点与第二天00点插值
                for minute_idx in range(4):
                    ratio = minute_idx / 4.0
                    interpolated = hour_23_data + (next_day_00_data - hour_23_data) * ratio
                    daily_97_points.append({
                        'time': f"23:{minute_idx*15:02d}",
                        'data': interpolated
                    })
                
                # 添加2400点（第二天00点数据）
                daily_97_points.append({
                    'time': "24:00",
                    'data': next_day_00_data
                })
            else:
                # 数据缺失处理
                for minute_idx in range(4):
                    daily_97_points.append({
                        'time': f"23:{minute_idx*15:02d}",
                        'data': None
                    })
                daily_97_points.append({
                    'time': "24:00",
                    'data': None
                })
            
            result_data.append({
                'date': date,
                'hourly_97_points': daily_97_points
            })
        
        return result_data
    
    def export_results(self, results, city_name, start_date, end_date):
        """导出结果到文件"""
        if not results:
            print("❌ 没有数据可导出")
            return
        
        output_file = f"{city_name}_{start_date}_{end_date}_97points.csv"
        output_path = os.path.join(os.path.dirname(self.csv_file_path), output_file)
        
        # 转换为DataFrame格式
        export_data = []
        for day_result in results:
            date = day_result['date']
            for point in day_result['hourly_97_points']:
                export_data.append({
                    '日期': date,
                    '时间': point['time'],
                    '数据': point['data']
                })
        
        df = pd.DataFrame(export_data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        print(f"✅ 结果已导出到: {output_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("吉林CMA实测气象数据解析器")
    print("=" * 60)
    
    # CSV文件路径
    csv_file = "吉林cma实测气象.csv"
    csv_path = os.path.join(os.path.dirname(__file__), csv_file)
    
    if not os.path.exists(csv_path):
        print(f"❌ 文件不存在: {csv_path}")
        return
    
    # 创建解析器
    parser = JilinWeatherParser(csv_path)
    
    # 示例使用
    city_name = "长春市"
    start_date = "20250101"
    end_date = "20250102"
    
    print(f"\n示例解析: {city_name}, {start_date} - {end_date}")
    
    # 解析数据
    results = parser.parse_city_weather(city_name, start_date, end_date)
    
    if results:
        print(f"✅ 解析完成，共 {len(results)} 天数据")
        
        # 导出结果
        parser.export_results(results, city_name, start_date, end_date)
        
        # 显示部分结果
        if results:
            first_day = results[0]
            print(f"\n{first_day['date']} 的前10个时间点:")
            for i, point in enumerate(first_day['hourly_97_points'][:10]):
                print(f"  {point['time']}: {point['data']}")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
