#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试横表格式和SQL输出功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    columns = [
        "station_id", "station_name", "station_type", "province", "city", "district", 
        "lon", "lat", "altitude", "pressure_altitude", "adcode", "note", "update_time", 
        "station_id", "time", "prs", "prs_sea", "win_s_max", "win_d_s_max", 
        "win_s_lnst_max", "win_d_lnst_max", "win_s_avg_2mi", "win_d_avg_2mi", 
        "tem", "rhu", "pre_1h", "vis", "update_time"
    ]
    
    data = []
    
    # 生成长春市2天数据，3个站点
    for day in range(2):
        for hour in range(24):
            for station in range(3):
                time_str = f"2025/1/{day+1} {hour:02d}:00:00"
                
                # 模拟真实的气象变化
                base_temp = -15.0 + hour * 0.8 + day * 2 + np.random.normal(0, 0.5)
                base_humidity = 75 + np.random.normal(0, 3)
                base_rainfall = max(0, np.random.exponential(0.05))
                base_windspeed = 2.0 + np.random.normal(0, 0.5)
                
                row = [
                    f"5418{station+1}", f"长春站{station+1}", "A", "吉林省", "长春市", "长春市",
                    "125.3", "43.8", "200", "200", "220100", "", "2024/11/19 18:13:46",
                    f"5418{station+1}", time_str,
                    round(1015 + np.random.normal(0, 1), 1),     # prs
                    round(1028 + np.random.normal(0, 1), 1),     # prs_sea
                    round(3.0 + np.random.normal(0, 0.8), 1),    # win_s_max
                    round(180 + np.random.normal(0, 30), 0),     # win_d_s_max
                    round(4.0 + np.random.normal(0, 1.0), 1),    # win_s_lnst_max
                    round(200 + np.random.normal(0, 40), 0),     # win_d_lnst_max
                    round(max(0, base_windspeed), 1),            # win_s_avg_2mi 风速
                    round(190 + np.random.normal(0, 20), 0),     # win_d_avg_2mi
                    round(base_temp, 1),                         # tem 温度
                    round(max(30, min(95, base_humidity)), 0),   # rhu 湿度
                    round(base_rainfall, 1),                     # pre_1h 降雨
                    "10000",                                     # vis
                    "2025/4/16 19:50:20"
                ]
                data.append(row)
    
    df = pd.DataFrame(data, columns=columns)
    df.to_csv("吉林cma实测气象.csv", index=False, encoding='utf-8-sig')
    
    print(f"✅ 测试数据已创建: {df.shape}")
    print(f"🏙️  城市: {df['city'].unique()}")
    print(f"📅 时间范围: {df['time'].min()} 到 {df['time'].max()}")
    
    return df

def test_excel_horizontal():
    """测试Excel横表输出"""
    print("\n🧪 测试1: Excel横表格式输出")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        print("📊 正在生成Excel横表...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250102",
            weather_indicator=None,  # 所有主要指标
            city_id=None,
            output_format='excel'
        )
        
        if results:
            print("✅ Excel横表生成成功！")
            
            # 检查生成的Excel文件
            excel_file = "长春市_20250101_20250102_横表.xlsx"
            if os.path.exists(excel_file):
                print(f"📄 Excel文件: {excel_file}")
                
                # 读取Excel文件检查格式
                try:
                    xl_file = pd.ExcelFile(excel_file)
                    print(f"📋 工作表: {xl_file.sheet_names}")
                    
                    # 读取主数据表
                    main_data = pd.read_excel(excel_file, sheet_name='气象数据横表')
                    print(f"📊 横表数据形状: {main_data.shape}")
                    print(f"📋 列名（前10个）: {list(main_data.columns)[:10]}")
                    
                    # 检查时间点列
                    time_columns = [col for col in main_data.columns if col.startswith('t')]
                    print(f"⏰ 时间点列数量: {len(time_columns)}")
                    print(f"⏰ 时间点范围: {time_columns[0]} - {time_columns[-1]}")
                    
                    # 显示部分数据
                    print("\n📄 横表数据样本:")
                    print(main_data[['城市', '日期', '指标', 't0000', 't0015', 't0030']].head())
                    
                except Exception as e:
                    print(f"❌ 读取Excel文件失败: {e}")
            else:
                print("❌ Excel文件未生成")
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_sql_output():
    """测试SQL输出"""
    print("\n🧪 测试2: SQL文件输出")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        print("📊 正在生成SQL文件...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",  # 只测试1天
            weather_indicator=None,  # 所有主要指标
            city_id="2201",  # 长春市ID
            output_format='sql'
        )
        
        if results:
            print("✅ SQL文件生成成功！")
            
            # 检查生成的SQL文件
            sql_file = "长春市_20250101_20250101_insert.sql"
            if os.path.exists(sql_file):
                print(f"📄 SQL文件: {sql_file}")
                
                # 读取SQL文件内容
                try:
                    with open(sql_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.split('\n')
                    print(f"📊 SQL文件行数: {len(lines)}")
                    
                    # 统计INSERT语句数量
                    insert_count = content.count('INSERT INTO')
                    print(f"📊 INSERT语句数量: {insert_count}")
                    
                    # 显示文件头部
                    print("\n📄 SQL文件头部:")
                    for line in lines[:10]:
                        if line.strip():
                            print(f"  {line}")
                    
                    # 显示第一个INSERT语句
                    print("\n📄 第一个INSERT语句（部分）:")
                    for line in lines:
                        if line.startswith('INSERT INTO'):
                            print(f"  {line[:100]}...")
                            break
                    
                except Exception as e:
                    print(f"❌ 读取SQL文件失败: {e}")
            else:
                print("❌ SQL文件未生成")
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_single_indicator():
    """测试单一指标输出"""
    print("\n🧪 测试3: 单一指标（温度）输出")
    print("=" * 40)
    
    try:
        from interactive_weather_parser import parse_weather_data
        
        # 测试Excel格式
        print("📊 正在生成温度指标Excel...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator="tem",  # 只处理温度
            city_id=None,
            output_format='excel'
        )
        
        if results:
            print("✅ 温度指标Excel生成成功！")
            excel_file = "长春市_20250101_20250101_tem_横表.xlsx"
            if os.path.exists(excel_file):
                print(f"📄 Excel文件: {excel_file}")
        
        # 测试SQL格式
        print("\n📊 正在生成温度指标SQL...")
        results = parse_weather_data(
            csv_file="吉林cma实测气象.csv",
            city_name="长春市",
            start_date="20250101",
            end_date="20250101",
            weather_indicator="tem",  # 只处理温度
            city_id="2201",
            output_format='sql'
        )
        
        if results:
            print("✅ 温度指标SQL生成成功！")
            sql_file = "长春市_20250101_20250101_tem_insert.sql"
            if os.path.exists(sql_file):
                print(f"📄 SQL文件: {sql_file}")
                
                # 检查SQL内容
                with open(sql_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                insert_count = content.count('INSERT INTO')
                print(f"📊 INSERT语句数量: {insert_count} (应该是1条，type=2表示温度)")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例:")
    print("=" * 30)
    
    print("1️⃣  交互式使用:")
    print("   python interactive_weather_parser.py")
    print("   按提示选择输出格式和指标")
    
    print("\n2️⃣  命令行使用 - Excel横表:")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --format excel")
    
    print("\n3️⃣  命令行使用 - SQL文件:")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --format sql --city-id 2201")
    
    print("\n4️⃣  单一指标:")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102 --indicator tem --format sql --city-id 2201")
    
    print("\n📊 气象指标说明:")
    indicators = {
        'tem': '温度 (type=2)',
        'rhu': '湿度 (type=1)',
        'pre_1h': '降雨量 (type=3)',
        'win_s_avg_2mi': '风速 (type=4)'
    }
    
    for code, desc in indicators.items():
        print(f"   {code}: {desc}")

def main():
    """主测试函数"""
    print("🧪 横表格式和SQL输出功能测试")
    print("=" * 50)
    
    # 创建测试数据
    create_test_data()
    
    # 测试1: Excel横表
    test_excel_horizontal()
    
    # 测试2: SQL输出
    test_sql_output()
    
    # 测试3: 单一指标
    test_single_indicator()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    # 列出生成的文件
    print("\n📋 生成的文件:")
    files = [f for f in os.listdir('.') if f.startswith('长春市') and (f.endswith('.xlsx') or f.endswith('.sql'))]
    for file in files:
        print(f"  📄 {file}")

if __name__ == "__main__":
    main()
