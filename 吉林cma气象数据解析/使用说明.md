# 吉林CMA实测气象数据解析器 - 使用说明 (Excel版本)

## 📋 真实数据格式说明

根据您提供的真实数据格式，解析器已经适配以下数据结构：

### 数据样本
```
"延边朝鲜族自治州","敦化市","128.2","43.37","526","526","222403",,"2024/11/19 18:13:46","54186","2023/1/1 00:00:00","960.4","1028","1.5","212","2.9","260","1.6","264","-14.7","83","0","999999","2025/4/16 19:50:20"
```

### 列名对应
```
"station_id","station_name","station_type","province","city","district","lon","lat","altitude","pressure_altitude","adcode","note","update_time","station_id","time","prs","prs_sea","win_s_max","win_d_s_max","win_s_lnst_max","win_d_lnst_max","win_s_avg_2mi","win_d_avg_2mi","tem","rhu","pre_1h","vis","update_time"
```

## 🔧 解析器修改要点

### 1. 城市筛选
- 使用 `city` 列进行城市筛选
- 支持模糊匹配（如"延边朝鲜族自治州"）

### 2. 时间处理
- 使用 `time` 列，格式为 "2023/1/1 00:00:00"
- 自动转换为 pandas datetime 格式
- 按日期和小时分组处理

### 3. 气象要素详细说明

| 指标代码 | 中文名称 | 单位 | 说明 | 常用程度 |
|----------|----------|------|------|----------|
| `tem` | 温度 | °C | 空气温度 | ⭐⭐⭐⭐⭐ |
| `rhu` | 相对湿度 | % | 空气相对湿度 | ⭐⭐⭐⭐⭐ |
| `prs` | 气压 | hPa | 本站气压 | ⭐⭐⭐⭐ |
| `prs_sea` | 海平面气压 | hPa | 海平面气压 | ⭐⭐⭐ |
| `win_s_avg_2mi` | 2分钟平均风速 | m/s | 2分钟平均风速 | ⭐⭐⭐⭐ |
| `win_d_avg_2mi` | 2分钟平均风向 | ° | 2分钟平均风向 | ⭐⭐⭐ |
| `win_s_max` | 最大风速 | m/s | 最大风速 | ⭐⭐⭐ |
| `win_d_s_max` | 最大风速风向 | ° | 最大风速对应风向 | ⭐⭐ |
| `win_s_lnst_max` | 瞬时最大风速 | m/s | 瞬时最大风速 | ⭐⭐ |
| `win_d_lnst_max` | 瞬时最大风速风向 | ° | 瞬时最大风速对应风向 | ⭐⭐ |
| `pre_1h` | 1小时降水量 | mm | 1小时累计降水量 | ⭐⭐⭐⭐ |
| `vis` | 能见度 | m | 水平能见度 | ⭐⭐⭐ |

#### 推荐的常用指标组合：
- **基础气象**: `tem`, `rhu`, `prs`
- **风力分析**: `win_s_avg_2mi`, `win_d_avg_2mi`
- **降水分析**: `pre_1h`, `rhu`
- **完整分析**: 所有指标

## 🆕 新功能特性

### 1. Excel输出格式 📊
- 生成 `.xlsx` 格式文件，替代原来的CSV格式
- 包含多个工作表：主数据、汇总统计、专项指标
- 自动生成数据统计和图表数据

### 2. 气象指标选择 🎯
- 支持指定单一气象指标进行解析
- 可选择温度、湿度、气压等12种指标
- 未指定时处理所有可用指标

## 🚀 使用方法

### 方法1：交互式使用（推荐）
```bash
cd "吉林cma气象数据解析"
python interactive_weather_parser.py
```

交互式输入示例：
```
请输入城市名称（如：长春市）: 长春市
请输入开始日期（如：20250101）: 20250101
请输入结束日期（如：20250102）: 20250102

📊 可用的气象指标:
  tem: 温度
  rhu: 相对湿度
  prs: 气压
  ... (更多指标)

💡 提示：
  - 直接回车：处理所有气象指标
  - 输入指标代码：只处理指定指标（如：tem）

请输入气象指标代码（可选）: tem
```

### 方法2：命令行参数
```bash
# 处理所有指标
python interactive_weather_parser.py --city "长春市" --start 20250101 --end 20250102

# 只处理温度指标
python interactive_weather_parser.py --city "长春市" --start 20250101 --end 20250102 --indicator tem

# 只处理湿度指标
python interactive_weather_parser.py --city "延边朝鲜族自治州" --start 20250101 --end 20250102 --indicator rhu
```

### 方法3：Python代码
```python
from interactive_weather_parser import parse_weather_data

# 处理所有指标
results = parse_weather_data(
    csv_file="吉林cma实测气象.csv",
    city_name="长春市",
    start_date="20250101",
    end_date="20250102",
    weather_indicator=None  # 所有指标
)

# 只处理温度指标
results = parse_weather_data(
    csv_file="吉林cma实测气象.csv",
    city_name="长春市",
    start_date="20250101",
    end_date="20250102",
    weather_indicator="tem"  # 只处理温度
)
```

## 📊 输出格式

### Excel文件结构
生成的 `.xlsx` 文件包含多个工作表：

#### 1. 气象数据表（主表）
包含97个时间点的完整数据：

| 城市 | 日期 | 时间 | tem | rhu | prs | ... |
|------|------|------|-----|-----|-----|-----|
| 长春市 | 2025-01-01 | 00:00 | -14.7 | 83.0 | 960.4 | ... |
| 长春市 | 2025-01-01 | 00:15 | -14.5 | 83.2 | 960.6 | ... |
| 长春市 | 2025-01-01 | 00:30 | -14.3 | 83.4 | 960.8 | ... |
| ... | ... | ... | ... | ... | ... | ... |
| 长春市 | 2025-01-01 | 24:00 | -15.6 | 82.9 | 960.2 | ... |

#### 2. 数据汇总表
按日期汇总的统计信息：

| 日期 | 数据点数 | 城市 | tem_平均值 | tem_最大值 | tem_最小值 | rhu_平均值 | ... |
|------|----------|------|------------|------------|------------|------------|-----|
| 2025-01-01 | 97 | 长春市 | -14.2 | -10.5 | -18.3 | 82.5 | ... |
| 2025-01-02 | 97 | 长春市 | -13.8 | -9.8 | -17.9 | 83.1 | ... |

#### 3. 专项指标表（可选）
当指定单一指标时，生成专门的数据表：

| 日期 | 时间 | tem |
|------|------|-----|
| 2025-01-01 | 00:00 | -14.7 |
| 2025-01-01 | 00:15 | -14.5 |
| ... | ... | ... |

### 文件命名规则
- **所有指标**: `城市名_开始日期_结束日期_97points.xlsx`
- **单一指标**: `城市名_开始日期_结束日期_指标代码_97points.xlsx`

示例：
- `长春市_20250101_20250102_97points.xlsx`
- `长春市_20250101_20250102_tem_97points.xlsx`

## 🎯 核心算法

### 站点平均算法
```python
# 按日期和小时分组，计算多个站点的平均值
averaged_data = city_data.groupby(['date', 'hour'])[weather_columns].mean()
```

### 97点插值算法
```python
# 线性插值公式
for minute_idx in range(4):  # 00, 15, 30, 45分钟
    ratio = minute_idx / 4.0  # 0, 0.25, 0.5, 0.75
    interpolated_value = current_hour_value + (next_hour_value - current_hour_value) * ratio
```

### 23点特殊处理
```python
# 23点与第二天00点插值
if next_day_00_data is not None:
    # 使用第二天真实的00点数据
    interpolated_value = hour_23_value + (next_day_00_value - hour_23_value) * ratio
    
# 2400点直接使用第二天00点数据
t2400_value = next_day_00_value
```

## ⚠️ 注意事项

### 1. 日期格式匹配
- 确保CSV中的 `time` 列格式为 "YYYY/M/D HH:MM:SS"
- 如 "2023/1/1 00:00:00" 或 "2023/12/31 23:00:00"

### 2. 城市名称匹配
- 使用完整的城市名称，如 "延边朝鲜族自治州"
- 支持的城市：长春市、吉林市、四平市、辽源市、通化市、白山市、松原市、白城市、延边朝鲜族自治州

### 3. 数据完整性
- 确保每天有24小时的数据
- 缺失数据会在输出中标记为 null
- 至少需要20小时数据才能进行97点转换

### 4. 站点数据
- 自动识别同一城市的多个站点
- 按站点平均计算地市级数据
- 如果只有一个站点，直接使用该站点数据

## 🔍 故障排除

### 问题1：没有找到城市数据
**原因**：城市名称不匹配
**解决**：检查CSV文件中 `city` 列的实际城市名称

### 问题2：日期格式转换失败
**原因**：时间格式不匹配
**解决**：确保 `time` 列格式为 "YYYY/M/D HH:MM:SS"

### 问题3：生成的97点数据不完整
**原因**：原始数据不足24小时
**解决**：确保每天有完整的24小时数据

### 问题4：2400点数据异常
**原因**：缺少第二天00点数据
**解决**：确保日期范围包含连续的天数

## 📝 示例输出文件名
- `长春市_20250101_20250102_97points.csv`
- `延边朝鲜族自治州_20250101_20250101_97points.csv`

## 🎉 使用建议

1. **首次使用**：先用小范围日期测试（如1-2天）
2. **数据检查**：确认CSV文件格式正确
3. **城市确认**：先查看CSV中有哪些城市数据
4. **结果验证**：检查输出的97点数据是否合理

现在您可以使用修改后的解析器来处理真实的吉林CMA气象数据了！
