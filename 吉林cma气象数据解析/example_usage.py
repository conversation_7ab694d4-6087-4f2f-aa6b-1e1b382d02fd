#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用示例：吉林CMA气象数据解析器
演示如何使用解析器处理气象数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_demo_data():
    """创建演示用的气象数据"""
    print("📊 创建演示数据...")
    
    # 生成2天的数据
    dates = [datetime(2025, 1, 1), datetime(2025, 1, 2)]
    hours = list(range(24))
    
    # 吉林省主要城市
    cities = ["长春市", "吉林市", "四平市", "通化市"]
    
    # 每个城市3个站点
    stations_per_city = 3
    
    demo_data = []
    
    for date in dates:
        for hour in hours:
            for city in cities:
                for station_id in range(1, stations_per_city + 1):
                    # 生成模拟的气象数据
                    base_temp = -15 + hour * 0.5  # 基础温度随时间变化
                    temp = base_temp + np.random.normal(0, 2)  # 添加随机变化
                    
                    humidity = 65 + np.random.normal(0, 5)
                    wind_speed = 3 + np.random.normal(0, 1)
                    pressure = 1013 + np.random.normal(0, 5)
                    
                    demo_data.append({
                        '城市': city,
                        '站点编号': f"{city[:2]}{station_id:02d}",
                        '日期': date.strftime('%Y-%m-%d'),
                        '小时': hour,
                        '温度': round(temp, 1),
                        '湿度': round(max(0, min(100, humidity)), 1),
                        '风速': round(max(0, wind_speed), 1),
                        '气压': round(pressure, 1)
                    })
    
    df = pd.DataFrame(demo_data)
    
    # 保存演示数据
    demo_file = "吉林cma实测气象.csv"
    df.to_csv(demo_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 演示数据已保存: {demo_file}")
    print(f"📊 数据形状: {df.shape}")
    print(f"🏙️  包含城市: {df['城市'].unique()}")
    print(f"📅 日期范围: {df['日期'].min()} 到 {df['日期'].max()}")
    
    return demo_file

def demo_basic_usage():
    """演示基本使用方法"""
    print("\n" + "="*60)
    print("🌤️  基本使用演示")
    print("="*60)
    
    # 确保有演示数据
    demo_file = "吉林cma实测气象.csv"
    if not os.path.exists(demo_file):
        demo_file = create_demo_data()
    
    # 演示数据加载
    print("1️⃣  加载数据...")
    try:
        data = pd.read_csv(demo_file, encoding='utf-8-sig')
        print(f"   ✅ 成功加载 {data.shape[0]} 条记录")
        print(f"   📋 列名: {list(data.columns)}")
    except Exception as e:
        print(f"   ❌ 加载失败: {e}")
        return
    
    # 演示城市筛选
    print("\n2️⃣  城市数据筛选...")
    city_name = "长春市"
    city_data = data[data['城市'] == city_name]
    print(f"   🏙️  {city_name} 数据: {city_data.shape[0]} 条记录")
    print(f"   🏢 站点数量: {city_data['站点编号'].nunique()}")
    
    # 演示日期筛选
    print("\n3️⃣  日期范围筛选...")
    target_date = "2025-01-01"
    date_data = city_data[city_data['日期'] == target_date]
    print(f"   📅 {target_date} 数据: {date_data.shape[0]} 条记录")
    
    # 演示站点平均计算
    print("\n4️⃣  计算站点平均值...")
    numeric_columns = ['温度', '湿度', '风速', '气压']
    hourly_avg = date_data.groupby('小时')[numeric_columns].mean()
    print(f"   📊 24小时平均数据:")
    print(hourly_avg.head(5))
    
    return hourly_avg

def demo_97_points_conversion(hourly_data):
    """演示97点转换"""
    print("\n5️⃣  转换为97点数据...")
    
    if hourly_data is None or hourly_data.empty:
        print("   ❌ 没有小时数据可转换")
        return
    
    # 生成97个时间点
    time_points = []
    interpolated_data = []
    
    # 处理0-22点（92个点）
    for hour in range(23):
        if hour in hourly_data.index and hour + 1 in hourly_data.index:
            current_data = hourly_data.loc[hour]
            next_data = hourly_data.loc[hour + 1]
            
            # 生成4个15分钟间隔点
            for minute_idx in range(4):
                ratio = minute_idx / 4.0
                interpolated = current_data + (next_data - current_data) * ratio
                
                time_str = f"{hour:02d}:{minute_idx*15:02d}"
                time_points.append(time_str)
                interpolated_data.append(interpolated)
    
    # 处理23点（4个点）
    if 23 in hourly_data.index:
        hour_23_data = hourly_data.loc[23]
        # 假设第二天00点数据（实际应用中需要获取真实的第二天数据）
        next_day_00_data = hourly_data.loc[0]  # 这里用当天00点代替
        
        for minute_idx in range(4):
            ratio = minute_idx / 4.0
            interpolated = hour_23_data + (next_day_00_data - hour_23_data) * ratio
            
            time_str = f"23:{minute_idx*15:02d}"
            time_points.append(time_str)
            interpolated_data.append(interpolated)
        
        # 添加24:00点
        time_points.append("24:00")
        interpolated_data.append(next_day_00_data)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(interpolated_data, 
                           index=time_points, 
                           columns=hourly_data.columns)
    
    print(f"   ✅ 生成 {len(result_df)} 个时间点")
    print(f"   ⏰ 时间范围: {result_df.index[0]} - {result_df.index[-1]}")
    
    # 显示部分结果
    print(f"\n   📊 前10个时间点的温度数据:")
    for i in range(min(10, len(result_df))):
        time_point = result_df.index[i]
        temp = result_df.iloc[i]['温度']
        print(f"      {time_point}: {temp:.1f}°C")
    
    # 保存结果
    output_file = "长春市_20250101_97points.csv"
    result_df.to_csv(output_file, encoding='utf-8-sig')
    print(f"\n   💾 结果已保存: {output_file}")
    
    return result_df

def demo_command_line_usage():
    """演示命令行使用方法"""
    print("\n" + "="*60)
    print("💻 命令行使用演示")
    print("="*60)
    
    print("📝 使用方法:")
    print()
    print("1️⃣  交互式使用:")
    print("   python interactive_weather_parser.py")
    print("   然后按提示输入城市名称和日期")
    print()
    print("2️⃣  命令行参数:")
    print("   python interactive_weather_parser.py --city 长春市 --start 20250101 --end 20250102")
    print()
    print("3️⃣  Python代码调用:")
    print("   from interactive_weather_parser import parse_weather_data")
    print("   results = parse_weather_data('吉林cma实测气象.csv', '长春市', '20250101', '20250102')")
    print()
    
    print("📋 支持的城市:")
    cities = ["长春市", "吉林市", "四平市", "辽源市", "通化市", "白山市", "松原市", "白城市", "延边朝鲜族自治州"]
    for i, city in enumerate(cities, 1):
        print(f"   {i}. {city}")

def main():
    """主演示函数"""
    print("🌤️  吉林CMA气象数据解析器使用演示")
    print("="*60)
    
    # 创建演示数据
    create_demo_data()
    
    # 基本使用演示
    hourly_data = demo_basic_usage()
    
    # 97点转换演示
    demo_97_points_conversion(hourly_data)
    
    # 命令行使用演示
    demo_command_line_usage()
    
    print("\n" + "="*60)
    print("✅ 演示完成！")
    print("💡 您现在可以使用 interactive_weather_parser.py 处理真实数据")
    print("📖 详细说明请查看 README.md")

if __name__ == "__main__":
    main()
